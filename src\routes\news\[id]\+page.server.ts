import { error } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { news } from '$lib/server/db/schema';
import { eq, and } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
  const id = parseInt(params.id);

  if (isNaN(id)) {
    throw error(404, 'Article not found');
  }

  try {
    // Fetch the news article from the database
    // Only return published articles for public access
    const article = await db.select()
      .from(news)
      .where(and(
        eq(news.id, id),
        eq(news.published, true)
      ))
      .limit(1);

    if (article.length === 0) {
      throw error(404, 'Article not found');
    }

    console.log('Loaded article data:', article[0]);

    return {
      article: article[0]
    };
  } catch (err) {
    console.error('Error fetching news article:', err);
    throw error(500, 'Failed to fetch article');
  }
};
