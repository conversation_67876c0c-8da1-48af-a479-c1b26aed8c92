<script>
	import { createEventDispatcher } from 'svelte';
	import AudioUploader from './AudioUploader.svelte';
	import AudioPlayer from '../AudioPlayer.svelte';

	const dispatch = createEventDispatcher();

	// Props
	export let audioUrl = '';
	export let audioTitle = '';
	export let audioDuration = 0;
	export let audioType = '';
	export let audioSize = 0;
	export let disabled = false;

	// State
	let activeTab = 'upload';
	let urlInput = '';
	let titleInput = '';
	let showPreview = false;

	// Initialize inputs with current values
	$: {
		if (audioUrl && !urlInput) {
			urlInput = audioUrl;
		}
		if (audioTitle && !titleInput) {
			titleInput = audioTitle;
		}
	}

	/**
	 * Handle tab switching
	 */
	function switchTab(tab) {
		activeTab = tab;
	}

	/**
	 * Handle audio upload
	 */
	function handleUpload(event) {
		const audioData = event.detail;
		
		// Update component state
		audioUrl = audioData.audioUrl;
		audioType = audioData.type;
		audioSize = audioData.size;
		audioDuration = audioData.duration || 0;
		
		// Set default title if not provided
		if (!titleInput) {
			titleInput = audioData.originalName.replace(/\.[^/.]+$/, ''); // Remove extension
		}
		
		// Update URL input to match
		urlInput = audioData.audioUrl;
		
		// Dispatch change event
		dispatch('change', {
			audioUrl,
			audioTitle: titleInput,
			audioDuration,
			audioType,
			audioSize
		});
	}

	/**
	 * Handle URL input change
	 */
	function handleUrlChange() {
		audioUrl = urlInput;
		dispatch('change', {
			audioUrl,
			audioTitle: titleInput,
			audioDuration: 0, // Reset duration for external URLs
			audioType: '',
			audioSize: 0
		});
	}

	/**
	 * Handle title change
	 */
	function handleTitleChange() {
		audioTitle = titleInput;
		dispatch('change', {
			audioUrl,
			audioTitle,
			audioDuration,
			audioType,
			audioSize
		});
	}

	/**
	 * Remove audio
	 */
	function removeAudio() {
		audioUrl = '';
		audioTitle = '';
		audioDuration = 0;
		audioType = '';
		audioSize = 0;
		urlInput = '';
		titleInput = '';
		showPreview = false;
		
		dispatch('change', {
			audioUrl: '',
			audioTitle: '',
			audioDuration: 0,
			audioType: '',
			audioSize: 0
		});
	}

	/**
	 * Format file size
	 */
	function formatFileSize(bytes) {
		if (bytes === 0) return '';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	/**
	 * Format duration
	 */
	function formatDuration(seconds) {
		if (!seconds || isNaN(seconds)) return '';
		const mins = Math.floor(seconds / 60);
		const secs = Math.floor(seconds % 60);
		return `${mins}:${secs.toString().padStart(2, '0')}`;
	}
</script>

<div class="audio-selector">
	<div class="selector-header">
		<h3>Audio Content</h3>
		{#if audioUrl}
			<button 
				class="remove-button"
				on:click={removeAudio}
				{disabled}
				type="button"
				aria-label="Remove audio"
			>
				Remove Audio
			</button>
		{/if}
	</div>

	<!-- Current Audio Preview -->
	{#if audioUrl}
		<div class="current-audio">
			<div class="audio-info">
				<h4>Current Audio</h4>
				<div class="audio-details">
					{#if audioTitle}
						<p><strong>Title:</strong> {audioTitle}</p>
					{/if}
					{#if audioDuration}
						<p><strong>Duration:</strong> {formatDuration(audioDuration)}</p>
					{/if}
					{#if audioSize}
						<p><strong>Size:</strong> {formatFileSize(audioSize)}</p>
					{/if}
					{#if audioType}
						<p><strong>Type:</strong> {audioType.toUpperCase()}</p>
					{/if}
				</div>
			</div>
			
			<div class="preview-controls">
				<button 
					class="preview-button"
					on:click={() => showPreview = !showPreview}
					type="button"
				>
					{showPreview ? 'Hide' : 'Show'} Preview
				</button>
			</div>
			
			{#if showPreview}
				<div class="audio-preview">
					<AudioPlayer 
						{audioUrl}
						audioTitle={audioTitle || 'Audio Preview'}
						{audioDuration}
						showDownload={false}
					/>
				</div>
			{/if}
		</div>
	{/if}

	<!-- Tab Navigation -->
	<div class="tab-buttons">
		<button 
			class="tab-btn"
			class:active={activeTab === 'upload'}
			on:click={() => switchTab('upload')}
			{disabled}
			type="button"
		>
			Upload File
		</button>
		<button 
			class="tab-btn"
			class:active={activeTab === 'url'}
			on:click={() => switchTab('url')}
			{disabled}
			type="button"
		>
			External URL
		</button>
	</div>

	<!-- Tab Content -->
	<div class="tab-content">
		{#if activeTab === 'upload'}
			<div class="upload-tab">
				<p class="tab-description">
					Upload an audio file from your computer. Supported formats: MP3, WAV, OGG, M4A.
				</p>
				<AudioUploader 
					{disabled}
					on:upload={handleUpload}
				/>
			</div>
		{:else if activeTab === 'url'}
			<div class="url-tab">
				<p class="tab-description">
					Enter a direct URL to an audio file hosted elsewhere.
				</p>
				<div class="form-group">
					<label for="audio-url">Audio URL</label>
					<input
						id="audio-url"
						type="url"
						bind:value={urlInput}
						on:input={handleUrlChange}
						placeholder="https://example.com/audio.mp3"
						{disabled}
					/>
				</div>
			</div>
		{/if}
		
		<!-- Audio Title Input (shown for both tabs) -->
		<div class="form-group">
			<label for="audio-title">Audio Title (Optional)</label>
			<input
				id="audio-title"
				type="text"
				bind:value={titleInput}
				on:input={handleTitleChange}
				placeholder="Enter a title for the audio content"
				{disabled}
			/>
			<p class="help-text">
				This title will be displayed with the audio player. If left empty, the article title will be used.
			</p>
		</div>
	</div>
</div>

<style>
	.audio-selector {
		width: 100%;
		border: 1px solid var(--color-border-primary);
		border-radius: 8px;
		overflow: hidden;
		background: var(--color-surface-primary);
		color: var(--color-text-primary);
	}

	.selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		background-color: var(--color-surface-secondary);
		border-bottom: 1px solid var(--color-border-primary);
	}

	.selector-header h3 {
		margin: 0;
		font-size: 1.1rem;
		color: var(--color-text-primary);
	}

	.remove-button {
		padding: 0.5rem 1rem;
		background-color: var(--color-error);
		color: var(--color-text-inverse);
		border: none;
		border-radius: 4px;
		font-size: 0.9rem;
		cursor: pointer;
		transition: var(--transition-theme);
	}

	.remove-button:hover:not(:disabled) {
		background-color: var(--color-error-hover);
	}

	.current-audio {
		padding: 1rem;
		background-color: var(--color-surface-tertiary);
		border-bottom: 1px solid var(--color-border-primary);
	}

	.audio-info h4 {
		margin: 0 0 0.5rem 0;
		font-size: 1rem;
		color: var(--color-text-primary);
	}

	.audio-details {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 0.5rem;
		margin-bottom: 1rem;
	}

	.audio-details p {
		margin: 0;
		font-size: 0.9rem;
		color: var(--color-text-secondary);
	}

	.preview-controls {
		margin-bottom: 1rem;
	}

	.preview-button {
		padding: 0.5rem 1rem;
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
		border: none;
		border-radius: 4px;
		font-size: 0.9rem;
		cursor: pointer;
		transition: var(--transition-theme);
	}

	.preview-button:hover {
		background-color: var(--color-interactive-primary-hover);
	}

	.audio-preview {
		margin-top: 1rem;
	}

	.tab-buttons {
		display: flex;
		border-bottom: 1px solid var(--color-border-primary);
	}

	.tab-btn {
		flex: 1;
		padding: 1rem;
		border: none;
		background: none;
		color: var(--color-text-primary);
		cursor: pointer;
		border-bottom: 2px solid transparent;
		transition: var(--transition-theme);
		font-size: 1rem;
	}

	.tab-btn:hover:not(:disabled) {
		background-color: var(--color-surface-tertiary);
	}

	.tab-btn.active {
		border-bottom-color: var(--color-interactive-primary);
		color: var(--color-interactive-primary);
		font-weight: 500;
	}

	.tab-btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.tab-content {
		padding: 1rem;
	}

	.tab-description {
		margin: 0 0 1rem 0;
		color: var(--color-text-secondary);
		font-size: 0.95rem;
		line-height: 1.4;
	}

	.form-group {
		margin-bottom: 1rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 500;
		color: var(--color-text-primary);
	}

	.form-group input {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--color-border-primary);
		border-radius: 4px;
		font-size: 1rem;
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
	}

	.form-group input:focus {
		outline: none;
		border-color: var(--color-border-focus);
		box-shadow: 0 0 0 2px var(--color-shadow-focus);
	}

	.help-text {
		margin: 0.5rem 0 0 0;
		font-size: 0.85rem;
		color: var(--color-text-secondary);
		line-height: 1.3;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.selector-header {
			flex-direction: column;
			gap: 0.5rem;
			align-items: stretch;
		}

		.audio-details {
			grid-template-columns: 1fr;
		}

		.tab-btn {
			font-size: 0.9rem;
			padding: 0.75rem;
		}
	}
</style>
