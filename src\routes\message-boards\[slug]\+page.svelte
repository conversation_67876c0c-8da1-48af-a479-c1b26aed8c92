<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// State management
	let board: any = null;
	let topics: any[] = [];
	let pagination: any = null;
	let isLoading = true;
	let error = '';
	let currentPage = 1;

	// Utility functions
	function clearError() {
		error = '';
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return 'No activity';
		
		const date = new Date(dateString);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffMinutes = Math.floor(diffMs / (1000 * 60));

		if (diffMinutes < 1) return 'Just now';
		if (diffMinutes < 60) return `${diffMinutes}m ago`;
		if (diffHours < 24) return `${diffHours}h ago`;
		if (diffDays < 7) return `${diffDays}d ago`;
		
		return date.toLocaleDateString();
	}

	function formatCount(count: number): string {
		if (count === 0) return '0';
		if (count < 1000) return count.toString();
		if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
		return `${(count / 1000000).toFixed(1)}m`;
	}

	function navigateToTopic(topicSlug: string) {
		goto(`/message-boards/${data.boardSlug}/${topicSlug}`);
	}

	function goToPage(page: number) {
		currentPage = page;
		loadBoardData();
	}

	// Load board and topics data
	async function loadBoardData() {
		isLoading = true;
		clearError();

		try {
			const response = await fetch(`/api/message-boards/${data.boardSlug}?page=${currentPage}&limit=20`);
			const result = await response.json();

			if (response.ok && result.success) {
				board = result.data.board;
				topics = result.data.topics || [];
				pagination = result.data.pagination;
			} else {
				error = result.error || 'Failed to load board';
			}
		} catch (err) {
			console.error('Error loading board:', err);
			error = 'Failed to load board';
		} finally {
			isLoading = false;
		}
	}

	// Initialize
	onMount(() => {
		loadBoardData();
	});
</script>

<svelte:head>
	{#if board}
		<title>{board.name} - Message Boards - FWFC</title>
		<meta name="description" content="{board.description || `Discuss ${board.name} topics with fellow Finn Wolfhard fans`}" />
	{:else}
		<title>Message Board - FWFC</title>
	{/if}
</svelte:head>

<div class="board-page">
	<!-- Error Message -->
	{#if error}
		<ErrorMessage message={error} onDismiss={clearError} />
	{/if}

	<!-- Loading State -->
	{#if isLoading}
		<div class="loading-container">
			<LoadingSpinner />
			<p>Loading board...</p>
		</div>
	{:else if board}
		<!-- Board Header -->
		<header class="board-header" style="border-left-color: {board.color}">
			<div class="header-content">
				<div class="board-info">
					<div class="board-icon" style="color: {board.color}">
						{#if board.icon}
							<i class="fa {board.icon}" aria-hidden="true"></i>
						{:else}
							<i class="fa fa-comments" aria-hidden="true"></i>
						{/if}
					</div>
					<div class="board-details">
						<h1>{board.name}</h1>
						{#if board.description}
							<p class="board-description">{board.description}</p>
						{/if}
						<div class="board-stats">
							<span class="stat">
								<strong>{formatCount(board.stats.topicCount)}</strong> topics
							</span>
							<span class="stat">
								<strong>{formatCount(board.stats.postCount)}</strong> posts
							</span>
						</div>
					</div>
				</div>
				<div class="header-actions">
					<button
						class="btn secondary"
						onclick={() => goto('/message-boards')}
					>
						← Back to Boards
					</button>
				</div>
			</div>
		</header>

		<!-- Topics List -->
		<main class="topics-container">
			{#if topics.length === 0}
				<div class="empty-state">
					<i class="fa fa-comments fa-3x" aria-hidden="true"></i>
					<h3>No Topics Yet</h3>
					<p>Be the first to start a discussion in this board!</p>
				</div>
			{:else}
				<div class="topics-list">
					{#each topics as topic}
						<article 
							class="topic-card"
							class:pinned={topic.isPinned}
							class:locked={topic.isLocked}
							role="button"
							tabindex="0"
							onclick={() => navigateToTopic(topic.slug)}
							onkeydown={(e) => (e.key === 'Enter' || e.key === ' ') && navigateToTopic(topic.slug)}
							aria-label="View topic: {topic.title}"
						>
							<div class="topic-header">
								<div class="topic-status">
									{#if topic.isPinned}
										<i class="fa fa-thumbtack" aria-hidden="true" title="Pinned topic"></i>
									{/if}
									{#if topic.isLocked}
										<i class="fa fa-lock" aria-hidden="true" title="Locked topic"></i>
									{/if}
								</div>
								<div class="topic-info">
									<h2>{topic.title}</h2>
									{#if topic.description}
										<p class="topic-description">{topic.description}</p>
									{/if}
									<div class="topic-meta">
										<span class="author">by {topic.authorName || 'Unknown'}</span>
										<span class="created-date">{formatDate(topic.createdAt)}</span>
									</div>
								</div>
								<div class="topic-stats">
									<div class="stat">
										<span class="stat-value">{formatCount(topic.postCount || 0)}</span>
										<span class="stat-label">posts</span>
									</div>
									<div class="stat">
										<span class="stat-value">{formatCount(topic.viewCount || 0)}</span>
										<span class="stat-label">views</span>
									</div>
								</div>
								<div class="topic-activity">
									<span class="last-activity">{formatDate(topic.lastPostAt)}</span>
								</div>
							</div>
						</article>
					{/each}
				</div>

				<!-- Pagination -->
				{#if pagination && pagination.totalPages > 1}
					<nav class="pagination" aria-label="Topics pagination">
						<button
							class="btn secondary"
							onclick={() => goToPage(currentPage - 1)}
							disabled={!pagination.hasPrev}
							aria-label="Go to previous page"
						>
							← Previous
						</button>
						
						<span class="page-info">
							Page {pagination.page} of {pagination.totalPages}
						</span>
						
						<button
							class="btn secondary"
							onclick={() => goToPage(currentPage + 1)}
							disabled={!pagination.hasNext}
							aria-label="Go to next page"
						>
							Next →
						</button>
					</nav>
				{/if}
			{/if}
		</main>
	{/if}
</div>

<style>
	.board-page {
		min-height: 100vh;
		background: var(--theme-bg-primary);
		color: var(--theme-text-primary);
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.board-header {
		background: var(--theme-bg-secondary);
		border-bottom: 1px solid var(--theme-border-primary);
		border-left: 4px solid var(--theme-accent-primary);
		padding: 2rem;
	}

	.header-content {
		max-width: 1200px;
		margin: 0 auto;
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		gap: 2rem;
	}

	.board-info {
		display: flex;
		align-items: flex-start;
		gap: 1.5rem;
		flex: 1;
	}

	.board-icon {
		font-size: 2.5rem;
		width: 4rem;
		height: 4rem;
		display: flex;
		align-items: center;
		justify-content: center;
		background: var(--theme-bg-primary);
		border-radius: 12px;
		flex-shrink: 0;
		border: 1px solid var(--theme-border-secondary);
	}

	.board-details h1 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 2rem;
		font-weight: 700;
		line-height: 1.2;
	}

	.board-description {
		margin: 0 0 1rem 0;
		color: var(--theme-text-secondary);
		font-size: 1rem;
		line-height: 1.5;
	}

	.board-stats {
		display: flex;
		gap: 2rem;
	}

	.board-stats .stat {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.board-stats .stat strong {
		color: var(--theme-text-primary);
		font-weight: 600;
	}

	.header-actions {
		flex-shrink: 0;
	}

	.topics-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.empty-state i {
		color: var(--theme-text-tertiary);
		margin-bottom: 1.5rem;
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
		font-weight: 600;
	}

	.empty-state p {
		margin: 0;
		max-width: 400px;
		margin-left: auto;
		margin-right: auto;
		line-height: 1.6;
	}

	.topics-list {
		display: flex;
		flex-direction: column;
		gap: 1px;
		background: var(--theme-border-primary);
		border-radius: 8px;
		overflow: hidden;
	}

	.topic-card {
		background: var(--theme-bg-secondary);
		padding: 1.5rem;
		cursor: pointer;
		transition: all 0.2s ease;
		border: none;
		position: relative;
	}

	.topic-card:hover {
		background: var(--theme-bg-tertiary);
	}

	.topic-card:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: -2px;
	}

	.topic-card.pinned {
		background: var(--theme-warning-bg);
		border-left: 4px solid var(--theme-warning-border);
	}

	.topic-card.locked {
		opacity: 0.8;
	}

	.topic-header {
		display: grid;
		grid-template-columns: auto 1fr auto auto;
		gap: 1rem;
		align-items: center;
	}

	.topic-status {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.topic-info {
		min-width: 0;
	}

	.topic-info h2 {
		margin: 0 0 0.25rem 0;
		color: var(--theme-text-primary);
		font-size: 1.125rem;
		font-weight: 600;
		line-height: 1.3;
	}

	.topic-description {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.topic-meta {
		display: flex;
		gap: 1rem;
		font-size: 0.8rem;
		color: var(--theme-text-tertiary);
	}

	.author {
		font-weight: 500;
	}

	.topic-stats {
		display: flex;
		gap: 1.5rem;
		text-align: center;
	}

	.topic-stats .stat {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.25rem;
	}

	.stat-value {
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--theme-text-primary);
		line-height: 1;
	}

	.stat-label {
		font-size: 0.75rem;
		color: var(--theme-text-secondary);
		text-transform: uppercase;
		letter-spacing: 0.05em;
	}

	.topic-activity {
		text-align: right;
		min-width: 80px;
	}

	.last-activity {
		font-size: 0.8rem;
		color: var(--theme-text-tertiary);
		font-style: italic;
	}

	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 1rem;
		margin-top: 2rem;
		padding: 1rem;
	}

	.page-info {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	/* Button Styles */
	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		text-decoration: none;
		font-size: 0.9rem;
		line-height: 1;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.secondary {
		background: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border-primary);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.board-header {
			padding: 1.5rem 1rem;
		}

		.header-content {
			flex-direction: column;
			align-items: stretch;
		}

		.board-info {
			flex-direction: column;
			gap: 1rem;
			text-align: center;
		}

		.board-details h1 {
			font-size: 1.5rem;
		}

		.board-stats {
			justify-content: center;
		}

		.topics-container {
			padding: 1rem;
		}

		.topic-header {
			grid-template-columns: 1fr;
			gap: 0.75rem;
			text-align: left;
		}

		.topic-stats {
			justify-content: flex-start;
			gap: 1rem;
		}

		.topic-activity {
			text-align: left;
		}

		.pagination {
			flex-direction: column;
			gap: 0.5rem;
		}
	}

	@media (max-width: 480px) {
		.board-icon {
			width: 3rem;
			height: 3rem;
			font-size: 2rem;
		}

		.topic-card {
			padding: 1rem;
		}

		.topic-stats {
			gap: 0.75rem;
		}

		.stat-value {
			font-size: 1rem;
		}
	}

	/* High Contrast Mode Support */
	@media (prefers-contrast: high) {
		.topic-card {
			border: 1px solid var(--theme-border-primary);
		}

		.topic-card:hover {
			border-color: var(--theme-accent-primary);
		}
	}

	/* Reduced Motion Support */
	@media (prefers-reduced-motion: reduce) {
		.topic-card {
			transition: none;
		}
	}
</style>
