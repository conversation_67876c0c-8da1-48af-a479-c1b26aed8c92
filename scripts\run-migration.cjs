#!/usr/bin/env node

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get database path from environment or use default
const databasePath = process.env.DATABASE_URL || 'local.db';
const db = new Database(databasePath);

console.log(`Connected to database: ${databasePath}`);

// Create migrations table if it doesn't exist
db.exec(`
  CREATE TABLE IF NOT EXISTS migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    applied_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
  )
`);

// Function to check if migration has been applied
function isMigrationApplied(migrationName) {
  const result = db.prepare('SELECT COUNT(*) as count FROM migrations WHERE name = ?').get(migrationName);
  return result.count > 0;
}

// Function to mark migration as applied
function markMigrationApplied(migrationName) {
  db.prepare('INSERT INTO migrations (name) VALUES (?)').run(migrationName);
}

// Run the enhanced user management migration
async function runEnhancedUserManagementMigration() {
  const migrationName = '0001_enhanced_user_management';
  
  if (isMigrationApplied(migrationName)) {
    console.log(`Migration ${migrationName} has already been applied. Skipping.`);
    return;
  }

  console.log(`Applying migration: ${migrationName}`);

  try {
    // Begin transaction
    db.exec('BEGIN TRANSACTION');

    // Add new columns to users table
    console.log('Adding new columns to users table...');
    
    // Check if columns already exist before adding them
    const userTableInfo = db.prepare("PRAGMA table_info(users)").all();
    const existingColumns = userTableInfo.map(col => col.name);

    const newColumns = [
      { name: 'bio', sql: 'ALTER TABLE users ADD COLUMN bio TEXT' },
      { name: 'avatar_url', sql: 'ALTER TABLE users ADD COLUMN avatar_url TEXT' },
      { name: 'location', sql: 'ALTER TABLE users ADD COLUMN location TEXT' },
      { name: 'website', sql: 'ALTER TABLE users ADD COLUMN website TEXT' },
      { name: 'birth_date', sql: 'ALTER TABLE users ADD COLUMN birth_date TEXT' },
      { name: 'interests', sql: 'ALTER TABLE users ADD COLUMN interests TEXT DEFAULT \'[]\'' },
      { name: 'status', sql: 'ALTER TABLE users ADD COLUMN status TEXT DEFAULT \'active\' NOT NULL' },
      { name: 'is_simulated', sql: 'ALTER TABLE users ADD COLUMN is_simulated INTEGER DEFAULT 0 NOT NULL' },
      { name: 'simulated_personality', sql: 'ALTER TABLE users ADD COLUMN simulated_personality TEXT' },
      { name: 'last_active_at', sql: 'ALTER TABLE users ADD COLUMN last_active_at TEXT' }
    ];

    for (const column of newColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`Adding column: ${column.name}`);
        db.exec(column.sql);
      } else {
        console.log(`Column ${column.name} already exists, skipping.`);
      }
    }

    // Create audit_logs table
    console.log('Creating audit_logs table...');
    db.exec(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        admin_user_id INTEGER NOT NULL,
        action TEXT NOT NULL,
        target_type TEXT NOT NULL,
        target_id INTEGER,
        target_user_id INTEGER,
        details TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_user_id) REFERENCES users(id),
        FOREIGN KEY (target_user_id) REFERENCES users(id)
      )
    `);

    // Create scheduled_content table
    console.log('Creating scheduled_content table...');
    db.exec(`
      CREATE TABLE IF NOT EXISTS scheduled_content (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content_type TEXT NOT NULL,
        content_data TEXT NOT NULL,
        as_user_id INTEGER NOT NULL,
        scheduled_for TEXT NOT NULL,
        status TEXT DEFAULT 'pending' NOT NULL,
        created_by_user_id INTEGER NOT NULL,
        published_at TEXT,
        error_message TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (as_user_id) REFERENCES users(id),
        FOREIGN KEY (created_by_user_id) REFERENCES users(id)
      )
    `);

    // Create content_authorship table
    console.log('Creating content_authorship table...');
    db.exec(`
      CREATE TABLE IF NOT EXISTS content_authorship (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content_type TEXT NOT NULL,
        content_id INTEGER NOT NULL,
        actual_author_id INTEGER NOT NULL,
        display_author_id INTEGER NOT NULL,
        is_simulated INTEGER DEFAULT 1 NOT NULL,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (actual_author_id) REFERENCES users(id),
        FOREIGN KEY (display_author_id) REFERENCES users(id)
      )
    `);

 so much! 😍', '["username", "topic"]', '["enthusiastic", "friendly"]'],
      ['Supportive Reply', 'reply', 'I totally agree with you {username}! {topic} is definitely one of my favorites too.', '["username", "topic"]', '["supportive", "agreeable"]'],
      ['Question About Topic', 'question', 'Hey {username}, what do you think about {topic}? I\'d love to hear your thoughts!', '["username", "topic"]', '["curious", "engaging"]'],
      ['Excited Reaction', 'reaction', 'This is so cool! Thanks for sharing {username}! 🎉', '["username"]', '["excited", "grateful"]'],
      ['Thoughtful Response', 'comment', 'This really makes me think about {topic}. Thanks for posting this {username}!', '["username", "topic"]', '["thoughtful", "appreciative"]']
    ];

    for (const template of templates) {
      insertTemplate.run(...template);
    }

    // Mark migration as applied
    markMigrationApplied(migrationName);

    // Commit transaction
    db.exec('COMMIT');

    console.log(`Migration ${migrationName} applied successfully!`);

  } catch (error) {
    // Rollback on error
    db.exec('ROLLBACK');
    console.error(`Error applying migration ${migrationName}:`, error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await runEnhancedUserManagementMigration();
    console.log('All migrations completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    db.close();
  }
}

// Run if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { runEnhancedUserManagementMigration };
