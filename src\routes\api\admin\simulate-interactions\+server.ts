import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { db } from '$lib/server/db';
import { comments, users, news, gallery, contentAuthorship, auditLogs } from '$lib/server/db/schema';
import { eq, and } from 'drizzle-orm';

export const POST: RequestHandler = async ({ request, locals, getClientAddress }) => {
	try {
		// Check authentication
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ success: false, error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { userIds, contentType, contentId, message } = body;

		// Validate input
		if (!Array.isArray(userIds) || userIds.length === 0) {
			return json({ success: false, error: 'At least one user must be selected' }, { status: 400 });
		}

		if (!contentType || !['news', 'gallery'].includes(contentType)) {
			return json({ success: false, error: 'Invalid content type' }, { status: 400 });
		}

		if (!contentId || typeof contentId !== 'number') {
			return json({ success: false, error: 'Content ID is required' }, { status: 400 });
		}

		if (!message || typeof message !== 'string' || message.trim().length < 5) {
			return json({ success: false, error: 'Message must be at least 5 characters long' }, { status: 400 });
		}

		// Verify all users exist and are simulated
		const selectedUsers = await db
			.select()
			.from(users)
			.where(and(
				eq(users.isSimulated, true),
				eq(users.status, 'active')
			));

		const validUserIds = selectedUsers
			.filter(user => userIds.includes(user.id))
			.map(user => user.id);

		if (validUserIds.length === 0) {
			return json({ success: false, error: 'No valid simulated users found' }, { status: 400 });
		}

		// Verify content exists and is published
		let contentExists = false;
		if (contentType === 'news') {
			const newsItem = await db
				.select({ id: news.id })
				.from(news)
				.where(and(eq(news.id, contentId), eq(news.published, true)))
				.limit(1);
			contentExists = newsItem.length > 0;
		} else if (contentType === 'gallery') {
			const galleryItem = await db
				.select({ id: gallery.id })
				.from(gallery)
				.where(and(eq(gallery.id, contentId), eq(gallery.published, true)))
				.limit(1);
			contentExists = galleryItem.length > 0;
		}

		if (!contentExists) {
			return json({ success: false, error: 'Content not found or not published' }, { status: 400 });
		}

		// Create comments for each user
		const createdComments = [];
		const currentTime = new Date().toISOString();

		for (const userId of validUserIds) {
			try {
				// Create the comment
				const [comment] = await db
					.insert(comments)
					.values({
						content: message.trim(),
						userId: userId,
						itemType: contentType,
						itemId: contentId,
						createdAt: currentTime,
						approved: true // Auto-approve admin-generated comments
					})
					.returning();

				if (comment) {
					createdComments.push(comment);

					// Create content authorship record for audit trail
					await db
						.insert(contentAuthorship)
						.values({
							contentId: comment.id,
							contentType: 'comment',
							actualAuthorId: locals.user.id, // The admin who created it
							displayAuthorId: userId, // The simulated user it appears to be from
							isSimulated: true,
							createdAt: currentTime
						});

					// Log the action for audit
					await db
						.insert(auditLogs)
						.values({
							adminId: locals.user.id,
							action: 'simulate_interaction',
							targetType: 'comment',
							targetId: comment.id,
							details: JSON.stringify({
								simulatedUserId: userId,
								targetContentType: contentType,
								targetContentId: contentId,
								messageLength: message.trim().length
							}),
							ipAddress: getClientAddress(),
							userAgent: request.headers.get('user-agent') || '',
							riskLevel: 'low',
							createdAt: currentTime
						});
				}
			} catch (error) {
				console.error(`Failed to create comment for user ${userId}:`, error);
				// Continue with other users even if one fails
			}
		}

		if (createdComments.length === 0) {
			return json({ success: false, error: 'Failed to create any comments' }, { status: 500 });
		}

		return json({
			success: true,
			message: `Successfully created ${createdComments.length} comments`,
			data: {
				commentsCreated: createdComments.length,
				totalUsersRequested: userIds.length,
				validUsersProcessed: validUserIds.length
			}
		});

	} catch (error) {
		console.error('Error in simulate interactions API:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};
