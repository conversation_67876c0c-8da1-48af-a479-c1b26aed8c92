import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { boards, topics, posts, auditLogs } from '$lib/server/db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { canUserPerformAction, getUserRole } from '$lib/server/permissions';

// POST - Create new topic
export const POST: RequestHandler = async ({ params, request, locals, getClientAddress }) => {
	try {
		const boardSlug = params.slug;

		// Check authentication
		if (!locals.user) {
			return json({ 
				success: false, 
				error: 'Authentication required to create topics' 
			}, { status: 401 });
		}

		// Get board details
		const [board] = await db
			.select({
				id: boards.id,
				name: boards.name,
				slug: boards.slug,
				isActive: boards.isActive
			})
			.from(boards)
			.where(eq(boards.slug, boardSlug))
			.limit(1);

		if (!board) {
			return json({ 
				success: false, 
				error: 'Board not found' 
			}, { status: 404 });
		}

		if (!board.isActive) {
			return json({ 
				success: false, 
				error: 'This board is not active' 
			}, { status: 403 });
		}

		// Check user permissions
		const userRole = getUserRole(locals.user);
		const canCreateTopics = await canUserPerformAction(board.id, userRole, 'canCreateTopics');

		if (!canCreateTopics) {
			return json({ 
				success: false, 
				error: 'You do not have permission to create topics in this board' 
			}, { status: 403 });
		}

		// Parse request body
		const body = await request.json();
		const { title, description, content } = body;

		// Validate required fields
		if (!title || !content) {
			return json({ 
				success: false, 
				error: 'Title and content are required' 
			}, { status: 400 });
		}

		// Validate field lengths
		if (title.trim().length < 3 || title.trim().length > 200) {
			return json({ 
				success: false, 
				error: 'Title must be between 3 and 200 characters' 
			}, { status: 400 });
		}

		if (content.trim().length < 10) {
			return json({ 
				success: false, 
				error: 'Content must be at least 10 characters long' 
			}, { status: 400 });
		}

		if (description && description.trim().length > 500) {
			return json({ 
				success: false, 
				error: 'Description must be less than 500 characters' 
			}, { status: 400 });
		}

		// Generate slug from title
		const baseSlug = title.trim()
			.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.replace(/^-|-$/g, '');

		// Ensure slug is unique within the board
		let slug = baseSlug;
		let counter = 1;
		
		while (true) {
			const existingTopic = await db
				.select({ id: topics.id })
				.from(topics)
				.where(and(eq(topics.boardId, board.id), eq(topics.slug, slug)))
				.limit(1);

			if (existingTopic.length === 0) break;
			
			slug = `${baseSlug}-${counter}`;
			counter++;
		}

		// Create the topic and initial post in a transaction
		const currentTime = new Date().toISOString();
		
		// Insert topic
		const [newTopic] = await db
			.insert(topics)
			.values({
				boardId: board.id,
				title: title.trim(),
				description: description?.trim() || null,
				slug,
				authorId: locals.user.id,
				isPinned: false,
				isLocked: false,
				isArchived: false,
				viewCount: 0,
				postCount: 1, // Initial post
				lastPostAt: currentTime,
				createdAt: currentTime,
				updatedAt: currentTime
			})
			.returning();

		// Insert initial post
		const [newPost] = await db
			.insert(posts)
			.values({
				topicId: newTopic.id,
				authorId: locals.user.id,
				content: content.trim(),
				isDeleted: false,
				createdAt: currentTime,
				updatedAt: currentTime
			})
			.returning();

		// Update topic with last post reference
		await db
			.update(topics)
			.set({ 
				lastPostId: newPost.id,
				lastPostAt: currentTime,
				updatedAt: currentTime
			})
			.where(eq(topics.id, newTopic.id));

		// Log the action
		await db.insert(auditLogs).values({
			userId: locals.user.id,
			action: 'create_topic',
			resourceType: 'topic',
			resourceId: newTopic.id.toString(),
			details: JSON.stringify({
				boardId: board.id,
				boardSlug: board.slug,
				topicTitle: title.trim(),
				topicSlug: slug
			}),
			ipAddress: getClientAddress(),
			userAgent: request.headers.get('user-agent') || '',
			createdAt: currentTime
		});

		return json({
			success: true,
			data: {
				topic: {
					id: newTopic.id,
					title: newTopic.title,
					description: newTopic.description,
					slug: newTopic.slug,
					boardSlug: board.slug,
					createdAt: newTopic.createdAt
				}
			}
		});

	} catch (error) {
		console.error('Error creating topic:', error);
		return json({ 
			success: false, 
			error: 'Failed to create topic' 
		}, { status: 500 });
	}
};
