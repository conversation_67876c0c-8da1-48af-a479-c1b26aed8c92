import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// Check if user is authenticated and is admin
	if (!locals.user) {
		throw redirect(302, '/auth/login');
	}

	if (locals.user.role !== 'admin') {
		throw redirect(302, '/');
	}

	// Return minimal data - the component will load its own data
	return {
		user: locals.user
	};
};
