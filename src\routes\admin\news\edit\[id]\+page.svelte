<script>
	import { goto } from '$app/navigation';
	import { api } from '$lib/utils/api';
	import logger from '$lib/client/logger';
	import ImageSelector from '$lib/components/admin/ImageSelector.svelte';
	import AudioSelector from '$lib/components/admin/AudioSelector.svelte';
	import RichTextEditor from '$lib/components/admin/RichTextEditor.svelte';

	// Create a logger with context
	const log = logger.withContext('news-edit');

	// Props from page data
	export let data;

	// Form state - initialize with existing article data
	let article = {
		id: data.article.id,
		title: data.article.title,
		content: data.article.content,
		imageUrl: data.article.imageUrl || '',
		audioUrl: data.article.audioUrl || '',
		audioTitle: data.article.audioTitle || '',
		audioDuration: data.article.audioDuration || 0,
		audioType: data.article.audioType || '',
		audioSize: data.article.audioSize || 0,
		published: data.article.published
	};

	let loading = false;
	/** @type {string|null} */
	let error = null;
	/** @type {string|null} */
	let successMessage = null;

	/**
	 * Handle image selection from ImageSelector
	 */
	function handleImageSelect(event) {
		const { imageUrl } = event.detail;
		article.imageUrl = imageUrl;
	}

	/**
	 * Handle audio selection from AudioSelector
	 */
	function handleAudioSelect(event) {
		const audioData = event.detail;
		article.audioUrl = audioData.audioUrl || '';
		article.audioTitle = audioData.audioTitle || '';
		article.audioDuration = audioData.audioDuration || 0;
		article.audioType = audioData.audioType || '';
		article.audioSize = audioData.audioSize || 0;
		log.debug('Audio selected', { audioData });
	}

	/**
	 * Update the news article
	 */
	async function updateArticle() {
		if (!article.title || !article.content) {
			error = 'Title and content are required';
			return;
		}

		loading = true;
		error = null;
		successMessage = null;

		log.info('Updating news article', { id: article.id, title: article.title });
		const { success, data: updatedData, error: apiError } = await api.put(`/api/news/${article.id}`, {
			title: article.title,
			content: article.content,
			imageUrl: article.imageUrl,
			audioUrl: article.audioUrl,
			audioTitle: article.audioTitle,
			audioDuration: article.audioDuration,
			audioType: article.audioType,
			audioSize: article.audioSize,
			published: article.published
		});

		if (success && updatedData) {
			log.info('News article updated successfully', updatedData);
			successMessage = 'Article updated successfully!';
			// Update local article data
			article = { ...article, ...updatedData };
		} else {
			log.error('Failed to update news article', apiError);
			error = apiError || 'Failed to update news article';
		}

		loading = false;
	}

	/**
	 * Handle content change for rich text editor
	 */
	function handleContentChange(event) {
		article.content = event.detail.content;
	}
</script>

<svelte:head>
	<title>Edit News Article - Admin</title>
</svelte:head>

<div class="edit-news-container">
	<div class="header">
		<h1>Edit News Article</h1>
		<a href="/admin/news" class="btn secondary">← Back to News</a>
	</div>

	{#if error}
		<div class="error-message">
			<p>{error}</p>
			<button class="btn secondary" on:click={() => error = null}>Dismiss</button>
		</div>
	{/if}

	{#if successMessage}
		<div class="success-message">
			<p>{successMessage}</p>
			<button class="btn secondary" on:click={() => successMessage = null}>Dismiss</button>
		</div>
	{/if}

	<form on:submit|preventDefault={updateArticle} class="article-form">
		<div class="form-group">
			<label for="title">Title *</label>
			<input
				type="text"
				id="title"
				bind:value={article.title}
				required
				placeholder="Enter article title"
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label>Featured Image</label>
			<ImageSelector
				selectedImageUrl={article.imageUrl}
				on:select={handleImageSelect}
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<AudioSelector
				audioUrl={article.audioUrl}
				audioTitle={article.audioTitle}
				audioDuration={article.audioDuration}
				audioType={article.audioType}
				audioSize={article.audioSize}
				on:change={handleAudioSelect}
				disabled={loading}
			/>
		</div>

		<div class="form-group">
			<label for="content">Content *</label>
			<RichTextEditor
				bind:value={article.content}
				placeholder="Write your article content here..."
				disabled={loading}
				height={400}
				on:change={handleContentChange}
			/>
		</div>

		<div class="form-group checkbox">
			<label>
				<input
					type="checkbox"
					bind:checked={article.published}
					disabled={loading}
				/>
				Published
			</label>
		</div>

		<div class="form-actions">
			<button type="submit" class="btn primary" disabled={loading}>
				{loading ? 'Updating...' : 'Update Article'}
			</button>
			<a href="/admin/news" class="btn secondary">Cancel</a>
		</div>
	</form>
</div>

<style>
	.edit-news-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
		background-color: var(--color-bg-primary);
		color: var(--color-text-primary);
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		text-decoration: none;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		transition: var(--transition-theme);
	}

	.primary {
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
	}

	.primary:hover:not(:disabled) {
		background-color: var(--color-interactive-primary-hover);
	}

	.secondary {
		background-color: var(--color-surface-secondary);
		color: var(--color-text-primary);
		border: 1px solid var(--color-border-primary);
	}

	.secondary:hover:not(:disabled) {
		background-color: var(--color-surface-tertiary);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.error-message {
		background-color: var(--color-error-bg);
		color: var(--color-error);
		border: 1px solid var(--color-error-border);
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.success-message {
		background-color: var(--color-success-bg);
		color: var(--color-success);
		border: 1px solid var(--color-success-border);
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.article-form {
		background-color: var(--color-surface-secondary);
		border: 1px solid var(--color-border-primary);
		padding: 2rem;
		border-radius: 8px;
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: bold;
		color: var(--color-text-primary);
	}

	.form-group input,
	.form-group textarea {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--color-border-primary);
		border-radius: 4px;
		font-size: 1rem;
		font-family: inherit;
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
	}

	.form-group input:focus,
	.form-group textarea:focus {
		outline: none;
		border-color: var(--color-border-focus);
		box-shadow: 0 0 0 2px var(--color-shadow-focus);
	}

	.form-group textarea {
		resize: vertical;
		min-height: 300px;
	}

	.form-group.checkbox label {
		display: flex;
		align-items: center;
		font-weight: normal;
	}

	.form-group.checkbox input {
		width: auto;
		margin-right: 0.5rem;
	}

	.help-text {
		font-size: 0.9rem;
		color: var(--color-text-secondary);
		margin-top: 0.5rem;
		margin-bottom: 0;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 2rem;
	}
</style>
