import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get the directory name for the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Define the upload directory paths
const UPLOAD_DIR = path.resolve('static/uploads');
const GALLERY_DIR = path.join(UPLOAD_DIR, 'gallery');
const THUMBNAIL_DIR = path.join(GALLERY_DIR, 'thumbnails');
const AUDIO_DIR = path.join(UPLOAD_DIR, 'audio');

// Ensure the upload directories exist
try {
  if (!fs.existsSync(UPLOAD_DIR)) {
    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
  }
  if (!fs.existsSync(GALLERY_DIR)) {
    fs.mkdirSync(GALLERY_DIR, { recursive: true });
  }
  if (!fs.existsSync(THUMBNAIL_DIR)) {
    fs.mkdirSync(THUMBNAIL_DIR, { recursive: true });
  }
  if (!fs.existsSync(AUDIO_DIR)) {
    fs.mkdirSync(AUDIO_DIR, { recursive: true });
  }
} catch (error) {
  console.error('Error creating upload directories:', error);
}

// Helper function to generate a unique filename
function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 10);
  const extension = path.extname(originalName);
  const safeName = path.basename(originalName, extension)
    .replace(/[^a-z0-9]/gi, '-')
    .toLowerCase();
  
  return `${safeName}-${timestamp}-${randomString}${extension}`;
}

// Helper function to create a thumbnail from an image
async function createThumbnail(
  sourcePath: string,
  targetPath: string,
  width = 300
): Promise<void> {
  try {
    await sharp(sourcePath)
      .resize(width)
      .toFile(targetPath);
  } catch (error) {
    console.error('Error creating thumbnail:', error);
    throw error;
  }
}

// Helper function to get audio metadata
async function getAudioMetadata(filePath: string): Promise<{ duration?: number }> {
  try {
    // For now, return empty metadata
    // In a production environment, you might use a library like node-ffprobe
    // or music-metadata to extract actual audio metadata
    return {};
  } catch (error) {
    console.error('Error getting audio metadata:', error);
    return {};
  }
}

// POST /api/upload - Upload a file (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return json({
        success: false,
        error: 'Request must be multipart/form-data'
      }, { status: 400 });
    }
    
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file');
    const category = formData.get('category') || 'gallery';
    const uploadType = formData.get('type') as string || 'gallery';
    
    // Validate the file
    if (!file || !(file instanceof File)) {
      return json({
        success: false,
        error: 'No file uploaded'
      }, { status: 400 });
    }
    
    // Check file type based on upload type
    const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const audioTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];

    let allowedTypes: string[];
    let fileCategory: 'image' | 'audio';

    if (uploadType === 'audio') {
      allowedTypes = audioTypes;
      fileCategory = 'audio';
    } else {
      allowedTypes = imageTypes;
      fileCategory = 'image';
    }

    if (!allowedTypes.includes(file.type)) {
      const typeList = fileCategory === 'audio'
        ? 'MP3, WAV, OGG, M4A'
        : 'JPEG, PNG, GIF, WebP';
      return json({
        success: false,
        error: `File type not allowed. Allowed types: ${typeList}`
      }, { status: 400 });
    }
    
    // Generate a unique filename
    const uniqueFilename = generateUniqueFilename(file.name);

    // Define file paths based on upload type
    let uploadPath: string;
    let thumbnailPath: string;
    let imageUrl: string;
    let thumbnailUrl: string;

    switch (uploadType) {
      case 'logo':
        uploadPath = path.resolve('static/images/logo.png');
        imageUrl = '/images/logo.png';
        thumbnailUrl = imageUrl; // No thumbnail for logo
        break;
      case 'profile':
        uploadPath = path.resolve('static/images/finn-profile.jpg');
        imageUrl = '/images/finn-profile.jpg';
        thumbnailUrl = imageUrl; // No thumbnail for profile
        break;
      case 'site-setting':
        uploadPath = path.join(UPLOAD_DIR, 'site-settings', uniqueFilename);
        imageUrl = `/uploads/site-settings/${uniqueFilename}`;
        thumbnailUrl = imageUrl; // No thumbnail for site settings
        // Ensure site-settings directory exists
        const siteSettingsDir = path.join(UPLOAD_DIR, 'site-settings');
        if (!fs.existsSync(siteSettingsDir)) {
          fs.mkdirSync(siteSettingsDir, { recursive: true });
        }
        break;
      case 'audio':
        uploadPath = path.join(AUDIO_DIR, uniqueFilename);
        imageUrl = `/uploads/audio/${uniqueFilename}`;
        thumbnailUrl = ''; // No thumbnail for audio
        break;
      default: // gallery
        uploadPath = path.join(GALLERY_DIR, uniqueFilename);
        thumbnailPath = path.join(THUMBNAIL_DIR, uniqueFilename);
        imageUrl = `/uploads/gallery/${uniqueFilename}`;
        thumbnailUrl = `/uploads/gallery/thumbnails/${uniqueFilename}`;
    }
    
    // Convert the file to a Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Save the file
    fs.writeFileSync(uploadPath, buffer);

    // Create a thumbnail only for gallery uploads
    if (uploadType === 'gallery' || !uploadType) {
      await createThumbnail(uploadPath, thumbnailPath);
    }

    // Get audio metadata for audio files
    let audioMetadata = {};
    if (uploadType === 'audio') {
      audioMetadata = await getAudioMetadata(uploadPath);
    }

    return json({
      success: true,
      data: {
        filename: uniqueFilename,
        originalName: file.name,
        size: file.size,
        type: file.type,
        imageUrl,
        thumbnailUrl,
        audioUrl: uploadType === 'audio' ? imageUrl : undefined,
        ...audioMetadata
      },
      url: imageUrl // For compatibility with ImageUploader component
    }, { status: 201 });
  } catch (error) {
    console.error('Error uploading file:', error);
    return json({
      success: false,
      error: 'Failed to upload file'
    }, { status: 500 });
  }
};
