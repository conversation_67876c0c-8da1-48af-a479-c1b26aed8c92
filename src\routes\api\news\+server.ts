import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { news } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/news - Get news articles
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get query parameters
    const limit = Number(url.searchParams.get('limit') || '10');
    const offset = Number(url.searchParams.get('offset') || '0');
    const all = url.searchParams.get('all') === 'true';

    // Build query
    let query = db.select().from(news);

    // If not requesting all articles, only show published ones
    // If requesting all, check if user is admin
    if (!all) {
      query = query.where(eq(news.published, true));
    } else {
      // Check if user is authenticated and has admin role for all articles
      if (!locals.user || locals.user.role !== 'admin') {
        return json({
          success: false,
          error: 'Unauthorized'
        }, { status: 401 });
      }
    }

    // Fetch news articles from the database
    const articles = await query
      .limit(limit)
      .offset(offset)
      .orderBy(news.createdAt);

    return json({
      success: true,
      data: articles
    });
  } catch (error) {
    console.error('Error fetching news articles:', error);
    return json({
      success: false,
      error: 'Failed to fetch news articles'
    }, { status: 500 });
  }
};

// POST /api/news - Create a new news article (admin only)
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.content) {
      return json({
        success: false,
        error: 'Title and content are required'
      }, { status: 400 });
    }
    
    // Insert new article into the database
    const result = await db.insert(news).values({
      title: body.title,
      content: body.content,
      imageUrl: body.imageUrl || null,
      audioUrl: body.audioUrl || null,
      audioTitle: body.audioTitle || null,
      audioDuration: body.audioDuration || null,
      audioType: body.audioType || null,
      audioSize: body.audioSize || null,
      authorId: locals.user.id,
      published: body.published || false
    }).returning();
    
    return json({
      success: true,
      data: result[0]
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating news article:', error);
    return json({
      success: false,
      error: 'Failed to create news article'
    }, { status: 500 });
  }
};
