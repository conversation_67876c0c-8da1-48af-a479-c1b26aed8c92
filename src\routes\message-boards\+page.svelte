<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// State management
	let boards: any[] = [];
	let isLoading = true;
	let error = '';

	// Utility functions
	function clearError() {
		error = '';
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return 'No activity yet';
		
		const date = new Date(dateString);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffMinutes = Math.floor(diffMs / (1000 * 60));

		if (diffMinutes < 1) return 'Just now';
		if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`;
		if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
		if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
		
		return date.toLocaleDateString();
	}

	function formatCount(count: number, singular: string, plural?: string): string {
		if (count === 0) return `No ${plural || singular + 's'}`;
		if (count === 1) return `1 ${singular}`;
		return `${count.toLocaleString()} ${plural || singular + 's'}`;
	}

	function navigateToBoard(boardSlug: string) {
		goto(`/message-boards/${boardSlug}`);
	}

	// Load boards data
	async function loadBoards() {
		isLoading = true;
		clearError();

		try {
			const response = await fetch('/api/message-boards');
			const result = await response.json();

			if (response.ok && result.success) {
				boards = result.data || [];
			} else {
				error = result.error || 'Failed to load message boards';
			}
		} catch (err) {
			console.error('Error loading boards:', err);
			error = 'Failed to load message boards';
		} finally {
			isLoading = false;
		}
	}

	// Initialize
	onMount(() => {
		loadBoards();
	});
</script>

<svelte:head>
	<title>Message Boards - FWFC</title>
	<meta name="description" content="Join the Finn Wolfhard Fan Community discussions. Connect with fellow fans, share your thoughts, and stay updated on the latest news and projects." />
</svelte:head>

<div class="message-boards-page">
	<header class="page-header">
		<div class="header-content">
			<h1>Message Boards</h1>
			<p class="header-subtitle">
				Connect with fellow fans, share your thoughts, and discuss everything Finn Wolfhard
			</p>
		</div>
	</header>

	<!-- Error Message -->
	{#if error}
		<ErrorMessage message={error} onDismiss={clearError} />
	{/if}

	<!-- Loading State -->
	{#if isLoading}
		<div class="loading-container">
			<LoadingSpinner />
			<p>Loading message boards...</p>
		</div>
	{:else}
		<!-- Boards List -->
		<div class="boards-container">
			{#if boards.length === 0}
				<div class="empty-state">
					<i class="fa fa-comments fa-3x" aria-hidden="true"></i>
					<h3>No Message Boards Available</h3>
					<p>Check back later for community discussions and updates.</p>
				</div>
			{:else}
				<div class="boards-grid">
					{#each boards as board}
						<article 
							class="board-card" 
							style="border-left-color: {board.color}"
							role="button"
							tabindex="0"
							onclick={() => navigateToBoard(board.slug)}
							onkeydown={(e) => (e.key === 'Enter' || e.key === ' ') && navigateToBoard(board.slug)}
							aria-label="View {board.name} board"
						>
							<div class="board-header">
								<div class="board-icon" style="color: {board.color}">
									{#if board.icon}
										<i class="fa {board.icon}" aria-hidden="true"></i>
									{:else}
										<i class="fa fa-comments" aria-hidden="true"></i>
									{/if}
								</div>
								<div class="board-info">
									<h2>{board.name}</h2>
									{#if board.description}
										<p class="board-description">{board.description}</p>
									{/if}
								</div>
								<div class="board-arrow">
									<i class="fa fa-chevron-right" aria-hidden="true"></i>
								</div>
							</div>
							
							<div class="board-stats">
								<div class="stat">
									<span class="stat-value">{board.stats.topicCount}</span>
									<span class="stat-label">{formatCount(board.stats.topicCount, 'Topic')}</span>
								</div>
								<div class="stat">
									<span class="stat-value">{board.stats.postCount}</span>
									<span class="stat-label">{formatCount(board.stats.postCount, 'Post')}</span>
								</div>
							</div>

							<div class="board-activity">
								{#if board.stats.lastTopic}
									<div class="last-topic">
										<span class="activity-label">Latest:</span>
										<span class="topic-title">{board.stats.lastTopic.title}</span>
									</div>
								{/if}
								<div class="last-activity">
									<span class="activity-time">{formatDate(board.stats.lastActivityAt)}</span>
								</div>
							</div>
						</article>
					{/each}
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	.message-boards-page {
		min-height: 100vh;
		background: var(--theme-bg-primary);
		color: var(--theme-text-primary);
	}

	.page-header {
		background: var(--theme-bg-secondary);
		border-bottom: 1px solid var(--theme-border-primary);
		padding: 3rem 2rem;
		text-align: center;
	}

	.header-content h1 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 2.5rem;
		font-weight: 700;
		letter-spacing: -0.025em;
	}

	.header-subtitle {
		color: var(--theme-text-secondary);
		margin: 0;
		font-size: 1.125rem;
		max-width: 600px;
		margin-left: auto;
		margin-right: auto;
		line-height: 1.6;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.empty-state i {
		color: var(--theme-text-tertiary);
		margin-bottom: 1.5rem;
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
		font-weight: 600;
	}

	.empty-state p {
		margin: 0;
		max-width: 400px;
		margin-left: auto;
		margin-right: auto;
		line-height: 1.6;
	}

	.boards-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.boards-grid {
		display: grid;
		gap: 1.5rem;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
	}

	.board-card {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border-primary);
		border-left: 4px solid var(--theme-accent-primary);
		border-radius: 8px;
		padding: 1.5rem;
		cursor: pointer;
		transition: all 0.2s ease;
		position: relative;
		overflow: hidden;
	}

	.board-card:hover {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-border-secondary);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	.board-card:focus {
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	.board-header {
		display: flex;
		align-items: flex-start;
		gap: 1rem;
		margin-bottom: 1.5rem;
	}

	.board-icon {
		font-size: 1.75rem;
		width: 3rem;
		height: 3rem;
		display: flex;
		align-items: center;
		justify-content: center;
		background: var(--theme-bg-primary);
		border-radius: 8px;
		flex-shrink: 0;
		border: 1px solid var(--theme-border-secondary);
	}

	.board-info {
		flex: 1;
		min-width: 0;
	}

	.board-info h2 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.375rem;
		font-weight: 600;
		line-height: 1.3;
	}

	.board-description {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		line-height: 1.5;
	}

	.board-arrow {
		color: var(--theme-text-tertiary);
		font-size: 1rem;
		flex-shrink: 0;
		transition: all 0.2s ease;
	}

	.board-card:hover .board-arrow {
		color: var(--theme-accent-primary);
		transform: translateX(4px);
	}

	.board-stats {
		display: flex;
		gap: 2rem;
		margin-bottom: 1.5rem;
		padding: 1rem 0;
		border-top: 1px solid var(--theme-border-secondary);
		border-bottom: 1px solid var(--theme-border-secondary);
	}

	.stat {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		flex: 1;
	}

	.stat-value {
		font-size: 1.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
		line-height: 1;
		margin-bottom: 0.25rem;
	}

	.stat-label {
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
		text-transform: uppercase;
		letter-spacing: 0.05em;
		font-weight: 500;
	}

	.board-activity {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.last-topic {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		min-width: 0;
	}

	.activity-label {
		font-size: 0.8rem;
		color: var(--theme-text-tertiary);
		font-weight: 500;
		flex-shrink: 0;
	}

	.topic-title {
		font-size: 0.9rem;
		color: var(--theme-text-primary);
		font-weight: 500;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		min-width: 0;
	}

	.last-activity {
		display: flex;
		justify-content: flex-end;
	}

	.activity-time {
		font-size: 0.8rem;
		color: var(--theme-text-tertiary);
		font-style: italic;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.page-header {
			padding: 2rem 1rem;
		}

		.header-content h1 {
			font-size: 2rem;
		}

		.header-subtitle {
			font-size: 1rem;
		}

		.boards-container {
			padding: 1rem;
		}

		.boards-grid {
			grid-template-columns: 1fr;
			gap: 1rem;
		}

		.board-card {
			padding: 1rem;
		}

		.board-header {
			margin-bottom: 1rem;
		}

		.board-icon {
			width: 2.5rem;
			height: 2.5rem;
			font-size: 1.5rem;
		}

		.board-info h2 {
			font-size: 1.25rem;
		}

		.board-stats {
			gap: 1rem;
			margin-bottom: 1rem;
		}

		.stat-value {
			font-size: 1.25rem;
		}

		.last-topic {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.25rem;
		}
	}

	@media (max-width: 480px) {
		.board-header {
			flex-direction: column;
			gap: 0.75rem;
			text-align: center;
		}

		.board-arrow {
			display: none;
		}

		.board-stats {
			justify-content: space-around;
		}
	}

	/* High Contrast Mode Support */
	@media (prefers-contrast: high) {
		.board-card {
			border-width: 2px;
		}

		.board-card:hover {
			border-width: 3px;
		}

		.board-icon {
			border-width: 2px;
		}
	}

	/* Reduced Motion Support */
	@media (prefers-reduced-motion: reduce) {
		.board-card {
			transition: none;
		}

		.board-arrow {
			transition: none;
		}

		.board-card:hover {
			transform: none;
		}

		.board-card:hover .board-arrow {
			transform: none;
		}
	}
</style>
