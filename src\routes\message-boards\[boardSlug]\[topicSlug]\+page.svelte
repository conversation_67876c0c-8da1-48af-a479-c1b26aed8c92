<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// State management
	let board: any = null;
	let topic: any = null;
	let posts: any[] = [];
	let pagination: any = null;
	let permissions: any = null;
	let user: any = null;
	let isLoading = true;
	let error = '';
	let currentPage = 1;

	// Utility functions
	function clearError() {
		error = '';
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return 'Unknown date';
		
		const date = new Date(dateString);
		return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();
	}

	function formatRelativeDate(dateString: string | null): string {
		if (!dateString) return 'Unknown time';
		
		const date = new Date(dateString);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffMinutes = Math.floor(diffMs / (1000 * 60));

		if (diffMinutes < 1) return 'Just now';
		if (diffMinutes < 60) return `${diffMinutes}m ago`;
		if (diffHours < 24) return `${diffHours}h ago`;
		if (diffDays < 7) return `${diffDays}d ago`;
		
		return date.toLocaleDateString();
	}

	function goToPage(page: number) {
		currentPage = page;
		loadTopicData();
	}

	// Load topic and posts data
	async function loadTopicData() {
		isLoading = true;
		clearError();

		try {
			const response = await fetch(`/api/message-boards/${data.boardSlug}/${data.topicSlug}?page=${currentPage}&limit=20`);
			const result = await response.json();

			if (response.ok && result.success) {
				board = result.data.board;
				topic = result.data.topic;
				posts = result.data.posts || [];
				pagination = result.data.pagination;
				permissions = result.data.permissions;
				user = result.data.user;
			} else {
				error = result.error || 'Failed to load topic';
			}
		} catch (err) {
			console.error('Error loading topic:', err);
			error = 'Failed to load topic';
		} finally {
			isLoading = false;
		}
	}

	// Initialize
	onMount(() => {
		loadTopicData();
	});
</script>

<svelte:head>
	{#if topic}
		<title>{topic.title} - {board?.name} - Message Boards - FWFC</title>
		<meta name="description" content="{topic.description || `Discussion topic: ${topic.title}`}" />
	{:else}
		<title>Topic - Message Boards - FWFC</title>
	{/if}
</svelte:head>

<div class="topic-page">
	<!-- Error Message -->
	{#if error}
		<ErrorMessage message={error} onDismiss={clearError} />
	{/if}

	<!-- Loading State -->
	{#if isLoading}
		<div class="loading-container">
			<LoadingSpinner />
			<p>Loading topic...</p>
		</div>
	{:else if topic && board}
		<!-- Topic Header -->
		<header class="topic-header">
			<div class="header-content">
				<nav class="breadcrumb" aria-label="Breadcrumb">
					<a href="/message-boards">Message Boards</a>
					<span class="separator">›</span>
					<a href="/message-boards/{board.slug}">{board.name}</a>
					<span class="separator">›</span>
					<span class="current">{topic.title}</span>
				</nav>

				<div class="topic-info">
					<h1>{topic.title}</h1>
					{#if topic.description}
						<p class="topic-description">{topic.description}</p>
					{/if}
					
					<div class="topic-meta">
						<div class="topic-status">
							{#if topic.isPinned}
								<span class="status-badge pinned">
									<i class="fa fa-thumbtack" aria-hidden="true"></i>
									Pinned
								</span>
							{/if}
							{#if topic.isLocked}
								<span class="status-badge locked">
									<i class="fa fa-lock" aria-hidden="true"></i>
									Locked
								</span>
							{/if}
						</div>
						
						<div class="topic-stats">
							<span class="stat">
								<i class="fa fa-eye" aria-hidden="true"></i>
								{topic.viewCount} views
							</span>
							<span class="stat">
								<i class="fa fa-comments" aria-hidden="true"></i>
								{topic.postCount} posts
							</span>
							<span class="stat">
								<i class="fa fa-user" aria-hidden="true"></i>
								by {topic.authorName || 'Unknown'}
							</span>
							<span class="stat">
								<i class="fa fa-clock" aria-hidden="true"></i>
								{formatRelativeDate(topic.createdAt)}
							</span>
						</div>
					</div>
				</div>

				<div class="header-actions">
					{#if user && permissions?.canReply && !topic.isLocked}
						<button class="btn primary">
							<i class="fa fa-reply" aria-hidden="true"></i>
							Reply
						</button>
					{:else if !user}
						<button
							class="btn primary"
							onclick={() => goto('/login?redirect=' + encodeURIComponent(window.location.pathname))}
						>
							<i class="fa fa-sign-in-alt" aria-hidden="true"></i>
							Login to Reply
						</button>
					{/if}
				</div>
			</div>
		</header>

		<!-- Posts List -->
		<main class="posts-container">
			{#if posts.length === 0}
				<div class="empty-state">
					<i class="fa fa-comments fa-3x" aria-hidden="true"></i>
					<h3>No Posts Yet</h3>
					<p>This topic doesn't have any posts yet.</p>
				</div>
			{:else}
				<div class="posts-list">
					{#each posts as post, index}
						<article class="post-card" class:first-post={index === 0}>
							<div class="post-header">
								<div class="author-info">
									<div class="author-avatar">
										<i class="fa fa-user" aria-hidden="true"></i>
									</div>
									<div class="author-details">
										<h3 class="author-name">{post.authorName || 'Unknown'}</h3>
										{#if post.authorRole}
											<span class="author-role">{post.authorRole}</span>
										{/if}
									</div>
								</div>
								<div class="post-meta">
									<time datetime={post.createdAt}>
										{formatDate(post.createdAt)}
									</time>
									{#if post.updatedAt !== post.createdAt}
										<span class="edited">(edited)</span>
									{/if}
								</div>
							</div>
							
							<div class="post-content">
								{post.content}
							</div>
						</article>
					{/each}
				</div>

				<!-- Pagination -->
				{#if pagination && pagination.totalPages > 1}
					<nav class="pagination" aria-label="Posts pagination">
						<button
							class="btn secondary"
							onclick={() => goToPage(currentPage - 1)}
							disabled={!pagination.hasPrev}
							aria-label="Go to previous page"
						>
							← Previous
						</button>
						
						<span class="page-info">
							Page {pagination.page} of {pagination.totalPages}
						</span>
						
						<button
							class="btn secondary"
							onclick={() => goToPage(currentPage + 1)}
							disabled={!pagination.hasNext}
							aria-label="Go to next page"
						>
							Next →
						</button>
					</nav>
				{/if}
			{/if}
		</main>
	{/if}
</div>

<style>
	.topic-page {
		min-height: 100vh;
		background: var(--theme-bg-primary);
		color: var(--theme-text-primary);
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.topic-header {
		background: var(--theme-bg-secondary);
		border-bottom: 1px solid var(--theme-border-primary);
		padding: 2rem;
	}

	.header-content {
		max-width: 1200px;
		margin: 0 auto;
	}

	.breadcrumb {
		margin-bottom: 1.5rem;
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
	}

	.breadcrumb a {
		color: var(--theme-accent-primary);
		text-decoration: none;
	}

	.breadcrumb a:hover {
		text-decoration: underline;
	}

	.separator {
		margin: 0 0.5rem;
		color: var(--theme-text-tertiary);
	}

	.current {
		color: var(--theme-text-primary);
		font-weight: 500;
	}

	.topic-info h1 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 2rem;
		font-weight: 700;
		line-height: 1.2;
	}

	.topic-description {
		margin: 0 0 1rem 0;
		color: var(--theme-text-secondary);
		font-size: 1rem;
		line-height: 1.5;
	}

	.topic-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 1rem;
		margin-top: 1rem;
	}

	.topic-status {
		display: flex;
		gap: 0.5rem;
	}

	.status-badge {
		display: inline-flex;
		align-items: center;
		gap: 0.25rem;
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.75rem;
		font-weight: 500;
		text-transform: uppercase;
		letter-spacing: 0.05em;
	}

	.status-badge.pinned {
		background: var(--theme-warning-bg);
		color: var(--theme-warning-text);
		border: 1px solid var(--theme-warning-border);
	}

	.status-badge.locked {
		background: var(--theme-error-bg);
		color: var(--theme-error-text);
		border: 1px solid var(--theme-error-border);
	}

	.topic-stats {
		display: flex;
		gap: 1rem;
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
	}

	.stat {
		display: flex;
		align-items: center;
		gap: 0.25rem;
	}

	.header-actions {
		position: absolute;
		top: 2rem;
		right: 2rem;
	}

	.posts-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.empty-state i {
		color: var(--theme-text-tertiary);
		margin-bottom: 1.5rem;
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
		font-weight: 600;
	}

	.posts-list {
		display: flex;
		flex-direction: column;
		gap: 1px;
		background: var(--theme-border-primary);
		border-radius: 8px;
		overflow: hidden;
	}

	.post-card {
		background: var(--theme-bg-secondary);
		padding: 1.5rem;
		position: relative;
	}

	.post-card.first-post {
		background: var(--theme-bg-tertiary);
		border-left: 4px solid var(--theme-accent-primary);
	}

	.post-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 1rem;
		gap: 1rem;
	}

	.author-info {
		display: flex;
		align-items: center;
		gap: 0.75rem;
	}

	.author-avatar {
		width: 2.5rem;
		height: 2.5rem;
		background: var(--theme-bg-primary);
		border: 1px solid var(--theme-border-secondary);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--theme-text-secondary);
		font-size: 1rem;
	}

	.author-name {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1rem;
		font-weight: 600;
		line-height: 1.2;
	}

	.author-role {
		font-size: 0.75rem;
		color: var(--theme-text-tertiary);
		text-transform: uppercase;
		letter-spacing: 0.05em;
		font-weight: 500;
	}

	.post-meta {
		text-align: right;
		font-size: 0.8rem;
		color: var(--theme-text-tertiary);
	}

	.edited {
		font-style: italic;
		margin-left: 0.5rem;
	}

	.post-content {
		color: var(--theme-text-primary);
		line-height: 1.6;
		white-space: pre-wrap;
		word-wrap: break-word;
	}

	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 1rem;
		margin-top: 2rem;
		padding: 1rem;
	}

	.page-info {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	/* Button Styles */
	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		text-decoration: none;
		font-size: 0.9rem;
		line-height: 1;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background: var(--theme-accent-primary);
		color: white;
	}

	.btn.primary:hover:not(:disabled) {
		background: var(--theme-accent-secondary);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border-primary);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.topic-header {
			padding: 1.5rem 1rem;
			position: relative;
		}

		.header-actions {
			position: static;
			margin-top: 1rem;
		}

		.topic-info h1 {
			font-size: 1.5rem;
		}

		.topic-meta {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.75rem;
		}

		.topic-stats {
			flex-wrap: wrap;
			gap: 0.75rem;
		}

		.posts-container {
			padding: 1rem;
		}

		.post-card {
			padding: 1rem;
		}

		.post-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.5rem;
		}

		.post-meta {
			text-align: left;
		}

		.pagination {
			flex-direction: column;
			gap: 0.5rem;
		}
	}

	/* High Contrast Mode Support */
	@media (prefers-contrast: high) {
		.post-card {
			border: 1px solid var(--theme-border-primary);
		}

		.author-avatar {
			border-width: 2px;
		}
	}

	/* Reduced Motion Support */
	@media (prefers-reduced-motion: reduce) {
		.btn.primary:hover:not(:disabled) {
			transform: none;
		}
	}
</style>
