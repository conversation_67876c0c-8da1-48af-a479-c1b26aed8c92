import { db } from './db';
import { boardPermissions } from './db/schema';
import { eq, and } from 'drizzle-orm';

export interface BoardPermissions {
	canView: boolean;
	canCreateTopics: boolean;
	canReply: boolean;
	canEditOwn: boolean;
	canDeleteOwn: boolean;
	canModerate: boolean;
}

/**
 * Get board permissions for a user role
 */
export async function getBoardPermissions(
	boardId: number, 
	userRole: string | null
): Promise<BoardPermissions> {
	// Default permissions for unauthenticated users
	const defaultPermissions: BoardPermissions = {
		canView: true,
		canCreateTopics: false,
		canReply: false,
		canEditOwn: false,
		canDeleteOwn: false,
		canModerate: false
	};

	// If no user role, return default (view-only) permissions
	if (!userRole) {
		return defaultPermissions;
	}

	try {
		// Get permissions for the user's role on this board
		const [permissions] = await db
			.select({
				canView: boardPermissions.canView,
				canCreateTopics: boardPermissions.canCreateTopics,
				canReply: boardPermissions.canReply,
				canEditOwn: boardPermissions.canEditOwn,
				canDeleteOwn: boardPermissions.canDeleteOwn,
				canModerate: boardPermissions.canModerate
			})
			.from(boardPermissions)
			.where(and(
				eq(boardPermissions.boardId, boardId),
				eq(boardPermissions.role, userRole)
			))
			.limit(1);

		if (permissions) {
			return {
				canView: Boolean(permissions.canView),
				canCreateTopics: Boolean(permissions.canCreateTopics),
				canReply: Boolean(permissions.canReply),
				canEditOwn: Boolean(permissions.canEditOwn),
				canDeleteOwn: Boolean(permissions.canDeleteOwn),
				canModerate: Boolean(permissions.canModerate)
			};
		}

		// If no specific permissions found, return default
		return defaultPermissions;

	} catch (error) {
		console.error('Error fetching board permissions:', error);
		return defaultPermissions;
	}
}

/**
 * Check if a user can perform a specific action on a board
 */
export async function canUserPerformAction(
	boardId: number,
	userRole: string | null,
	action: keyof BoardPermissions
): Promise<boolean> {
	const permissions = await getBoardPermissions(boardId, userRole);
	return permissions[action];
}

/**
 * Get user role from user object
 */
export function getUserRole(user: any): string | null {
	if (!user) return null;
	return user.role || null;
}
