import {
	sqliteTable,
	text,
	integer,
	blob,
	primaryKey,
	unique
} from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

import { relations } from 'drizzle-orm';

export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	username: text('username').notNull().unique(),
	displayName: text('display_name').notNull(),
	email: text('email').notNull().unique(),
	passwordHash: text('password_hash').notNull(),
	role: text('role', { enum: ['admin', 'moderator', 'user'] }).default('user').notNull(),
	// Enhanced profile fields
	bio: text('bio'),
	avatarUrl: text('avatar_url'),
	location: text('location'),
	website: text('website'),
	birthDate: text('birth_date'),
	interests: text('interests', { mode: 'json' }).$type<string[]>().default('[]'),
	// Account status and management
	status: text('status', { enum: ['active', 'inactive', 'suspended'] }).default('active').notNull(),
	isSimulated: integer('is_simulated', { mode: 'boolean' }).notNull().default(false),
	simulatedPersonality: text('simulated_personality', { mode: 'json' }).$type<{
		traits: string[];
		interests: string[];
		writingStyle: string;
		activityLevel: 'low' | 'medium' | 'high';
	}>(),
	lastActiveAt: text('last_active_at'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	preferences: text('preferences', { mode: 'json' }).$type<{
		highContrast: boolean;
		largeText: boolean;
		simplifiedInterface: boolean
	 }>().default('{"highContrast": false, "largeText": false, "simplifiedInterface": false}')
});

export const news = sqliteTable('news', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	content: text('content').notNull(),
	imageUrl: text('image_url'),
	audioUrl: text('audio_url'),
	audioTitle: text('audio_title'),
	audioDuration: integer('audio_duration'), // Duration in seconds
	audioType: text('audio_type'), // mp3, wav, ogg, etc.
	audioSize: integer('audio_size'), // File size in bytes
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	published: integer('published', { mode: 'boolean' }).notNull().default(false)
});

export const gallery = sqliteTable('gallery', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	description: text('description'),
	imageUrl: text('image_url').notNull(),
	thumbnailUrl: text('thumbnail_url').notNull(),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	published: integer('published', { mode: 'boolean' }).notNull().default(false)
});

export const messages = sqliteTable('messages', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false)
});

export const replies = sqliteTable('replies', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	messageId: integer('message_id').references(() => messages.id).notNull(),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false)
});

export const comments = sqliteTable('comments', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	userId: integer('user_id').references(() => users.id),
	content: text('content').notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	approved: integer('approved', { mode: 'boolean' }).notNull().default(false),
	itemType: text('item_type', { enum: ['news', 'gallery'] }).notNull(),
	itemId: integer('item_id').notNull()
});

export const siteSettings = sqliteTable('site_settings', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	category: text('category', {
		enum: [
			'general',
			'appearance',
			'accessibility',
			'social'
		]
	}).notNull(),
	settings: text('settings', { mode: 'json' }).notNull()
});

export const messageOfTheDay = sqliteTable('message_of_the_day', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	content: text('content').notNull(),
	active: integer('active', { mode: 'boolean' }).notNull().default(true),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

export const media = sqliteTable('media', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	filename: text('filename').notNull(),
	originalName: text('original_name').notNull(),
	path: text('path').notNull(),
	thumbnailPath: text('thumbnail_path'),
	type: text('type').notNull(), // image, video, document, etc.
	mimeType: text('mime_type').notNull(),
	size: integer('size').notNull(),
	width: integer('width'),
	height: integer('height'),
	alt: text('alt'),
	caption: text('caption'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	authorId: integer('author_id').references(() => users.id)
});

export const heroImages = sqliteTable('hero_images', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	subtitle: text('subtitle'),
	imageUrl: text('image_url').notNull(),
	active: integer('active', { mode: 'boolean' }).notNull().default(false),
	sortOrder: integer('sort_order').notNull().default(0),
	authorId: integer('author_id').references(() => users.id),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Audit logging for admin actions
export const auditLogs = sqliteTable('audit_logs', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	adminUserId: integer('admin_user_id').references(() => users.id).notNull(),
	action: text('action').notNull(), // 'create', 'update', 'delete', 'login', 'post_as_user', etc.
	targetType: text('target_type').notNull(), // 'user', 'news', 'gallery', 'comment', etc.
	targetId: integer('target_id'), // ID of the affected record
	targetUserId: integer('target_user_id').references(() => users.id), // User being acted upon or posted as
	details: text('details', { mode: 'json' }).$type<{
		before?: any;
		after?: any;
		metadata?: any;
	}>(),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Scheduled content for natural posting patterns
export const scheduledContent = sqliteTable('scheduled_content', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply', 'topic', 'post'] }).notNull(),
	contentData: text('content_data', { mode: 'json' }).notNull(), // The actual content to be posted
	asUserId: integer('as_user_id').references(() => users.id).notNull(), // User to post as
	scheduledFor: text('scheduled_for').notNull(), // ISO datetime string
	status: text('status', { enum: ['pending', 'published', 'failed', 'cancelled'] }).default('pending').notNull(),
	createdByUserId: integer('created_by_user_id').references(() => users.id).notNull(), // Admin who scheduled it
	publishedAt: text('published_at'),
	errorMessage: text('error_message'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Content authorship tracking for admin-posted content
export const contentAuthorship = sqliteTable('content_authorship', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply', 'topic', 'post'] }).notNull(),
	contentId: integer('content_id').notNull(),
	actualAuthorId: integer('actual_author_id').references(() => users.id).notNull(), // Admin who actually created it
	displayAuthorId: integer('display_author_id').references(() => users.id).notNull(), // User it appears to be from
	isSimulated: integer('is_simulated', { mode: 'boolean' }).notNull().default(true),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// AI-generated content review system
export const aiContentReviews = sqliteTable('ai_content_reviews', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply', 'topic', 'post'] }).notNull(),
	originalPrompt: text('original_prompt').notNull(),
	generatedContent: text('generated_content', { mode: 'json' }).notNull(), // JSON with title, content, description
	authenticityScore: integer('authenticity_score').notNull(),
	aiConfig: text('ai_config', { mode: 'json' }).notNull(), // JSON with tone, length, focusAreas, etc.
	targetUserId: integer('target_user_id').references(() => users.id).notNull(),
	reviewStatus: text('review_status', { enum: ['pending', 'approved', 'rejected', 'needs_revision'] }).notNull().default('pending'),
	reviewedById: integer('reviewed_by_id').references(() => users.id),
	reviewNotes: text('review_notes'),
	reviewedAt: text('reviewed_at'),
	createdById: integer('created_by_id').references(() => users.id).notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// AI content generation metrics and monitoring
export const aiGenerationMetrics = sqliteTable('ai_generation_metrics', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	contentType: text('content_type', { enum: ['news', 'gallery', 'comment', 'message', 'reply'] }).notNull(),
	promptLength: integer('prompt_length').notNull(),
	generatedLength: integer('generated_length').notNull(),
	authenticityScore: integer('authenticity_score').notNull(),
	tone: text('tone').notNull(),
	length: text('length').notNull(),
	focusAreas: text('focus_areas', { mode: 'json' }).$type<string[]>().default('[]'),
	generationTimeMs: integer('generation_time_ms').notNull(),
	success: integer('success', { mode: 'boolean' }).notNull(),
	errorMessage: text('error_message'),
	userId: integer('user_id').references(() => users.id).notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});



export const usersRelations = relations(users, ({ many }) => ({
	news: many(news),
	gallery: many(gallery),
	messages: many(messages),
	replies: many(replies),
	comments: many(comments),
	media: many(media),
	heroImages: many(heroImages),
	auditLogsAsAdmin: many(auditLogs, { relationName: 'adminAuditLogs' }),
	auditLogsAsTarget: many(auditLogs, { relationName: 'targetAuditLogs' }),
	scheduledContentAsUser: many(scheduledContent, { relationName: 'userScheduledContent' }),
	scheduledContentAsCreator: many(scheduledContent, { relationName: 'creatorScheduledContent' }),
	contentAuthorshipAsActual: many(contentAuthorship, { relationName: 'actualAuthorContent' }),
	contentAuthorshipAsDisplay: many(contentAuthorship, { relationName: 'displayAuthorContent' }),
	// Message board relations
	topics: many(topics),
	posts: many(posts),
	postsEdited: many(posts, { relationName: 'postEditor' }),
	postsDeleted: many(posts, { relationName: 'postDeleter' }),
	topicSubscriptions: many(topicSubscriptions),
	postReactions: many(postReactions),
	moderationActions: many(moderationActions)
}));

export const newsRelations = relations(news, ({ one, many }) => ({
	author: one(users, {
		fields: [news.authorId],
		references: [users.id]
	}),
	comments: many(comments, {
		filterForeignFields: (comment) => comment.itemType.equals('news')
	})
}));

export const galleryRelations = relations(gallery, ({ one, many }) => ({
	author: one(users, {
		fields: [gallery.authorId],
		references: [users.id]
	}),
	comments: many(comments, {
		filterForeignFields: (comment) => comment.itemType.equals('gallery')
	})
}));

export const messagesRelations = relations(messages, ({ one, many }) => ({
	user: one(users, {
		fields: [messages.userId],
		references: [users.id]
	}),
	replies: many(replies)
}));

export const repliesRelations = relations(replies, ({ one }) => ({
	message: one(messages, {
		fields: [replies.messageId],
		references: [messages.id]
	}),
	user: one(users, {
		fields: [replies.userId],
		references: [users.id]
	})
}));

export const commentsRelations = relations(comments, ({ one }) => ({
	user: one(users, {
		fields: [comments.userId],
		references: [users.id]
	})
}));

export const mediaRelations = relations(media, ({ one }) => ({
	author: one(users, {
		fields: [media.authorId],
		references: [users.id]
	})
}));

export const heroImagesRelations = relations(heroImages, ({ one }) => ({
	author: one(users, {
		fields: [heroImages.authorId],
		references: [users.id]
	})
}));

export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
	adminUser: one(users, {
		fields: [auditLogs.adminUserId],
		references: [users.id],
		relationName: 'adminAuditLogs'
	}),
	targetUser: one(users, {
		fields: [auditLogs.targetUserId],
		references: [users.id],
		relationName: 'targetAuditLogs'
	})
}));

export const scheduledContentRelations = relations(scheduledContent, ({ one }) => ({
	asUser: one(users, {
		fields: [scheduledContent.asUserId],
		references: [users.id],
		relationName: 'userScheduledContent'
	}),
	createdByUser: one(users, {
		fields: [scheduledContent.createdByUserId],
		references: [users.id],
		relationName: 'creatorScheduledContent'
	})
}));

export const contentAuthorshipRelations = relations(contentAuthorship, ({ one }) => ({
	actualAuthor: one(users, {
		fields: [contentAuthorship.actualAuthorId],
		references: [users.id],
		relationName: 'actualAuthorContent'
	}),
	displayAuthor: one(users, {
		fields: [contentAuthorship.displayAuthorId],
		references: [users.id],
		relationName: 'displayAuthorContent'
	})
}));

export const aiContentReviewsRelations = relations(aiContentReviews, ({ one }) => ({
	targetUser: one(users, {
		fields: [aiContentReviews.targetUserId],
		references: [users.id],
		relationName: 'aiContentTargetUser'
	}),
	reviewedBy: one(users, {
		fields: [aiContentReviews.reviewedById],
		references: [users.id],
		relationName: 'aiContentReviewer'
	}),
	createdBy: one(users, {
		fields: [aiContentReviews.createdById],
		references: [users.id],
		relationName: 'aiContentCreator'
	})
}));

export const aiGenerationMetricsRelations = relations(aiGenerationMetrics, ({ one }) => ({
	user: one(users, {
		fields: [aiGenerationMetrics.userId],
		references: [users.id],
		relationName: 'aiMetricsUser'
	})
}));

export const user = sqliteTable('user', {
	id: integer('id').primaryKey(),
	age: integer('age')
});

// Message Board System Tables

// Boards (Message Board Categories)
export const boards = sqliteTable('boards', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	name: text('name').notNull(),
	description: text('description'),
	slug: text('slug').notNull().unique(),
	icon: text('icon'), // Font Awesome icon class
	color: text('color'), // Hex color for theming
	position: integer('position').notNull().default(0),
	isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Topics (Discussion Threads)
export const topics = sqliteTable('topics', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	boardId: integer('board_id').notNull().references(() => boards.id, { onDelete: 'cascade' }),
	title: text('title').notNull(),
	slug: text('slug').notNull(),
	description: text('description'),
	authorId: integer('author_id').notNull().references(() => users.id),
	isPinned: integer('is_pinned', { mode: 'boolean' }).notNull().default(false),
	isLocked: integer('is_locked', { mode: 'boolean' }).notNull().default(false),
	isArchived: integer('is_archived', { mode: 'boolean' }).notNull().default(false),
	viewCount: integer('view_count').notNull().default(0),
	postCount: integer('post_count').notNull().default(0),
	lastPostId: integer('last_post_id').references(() => posts.id),
	lastPostAt: text('last_post_at'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
}, (table) => ({
	uniqueBoardSlug: unique().on(table.boardId, table.slug)
}));

// Posts (Messages within Topics)
export const posts = sqliteTable('posts', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	topicId: integer('topic_id').notNull().references(() => topics.id, { onDelete: 'cascade' }),
	parentPostId: integer('parent_post_id').references(() => posts.id),
	authorId: integer('author_id').notNull().references(() => users.id),
	content: text('content').notNull(),
	isFirstPost: integer('is_first_post', { mode: 'boolean' }).notNull().default(false),
	isEdited: integer('is_edited', { mode: 'boolean' }).notNull().default(false),
	editedAt: text('edited_at'),
	editedBy: integer('edited_by').references(() => users.id),
	editReason: text('edit_reason'),
	isDeleted: integer('is_deleted', { mode: 'boolean' }).notNull().default(false),
	deletedAt: text('deleted_at'),
	deletedBy: integer('deleted_by').references(() => users.id),
	deletionReason: text('deletion_reason'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`),
	updatedAt: text('updated_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Board Permissions (Role-based Access Control)
export const boardPermissions = sqliteTable('board_permissions', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	boardId: integer('board_id').notNull().references(() => boards.id, { onDelete: 'cascade' }),
	role: text('role', { enum: ['admin', 'moderator', 'user'] }).notNull(),
	canView: integer('can_view', { mode: 'boolean' }).notNull().default(true),
	canCreateTopics: integer('can_create_topics', { mode: 'boolean' }).notNull().default(true),
	canReply: integer('can_reply', { mode: 'boolean' }).notNull().default(true),
	canEditOwn: integer('can_edit_own', { mode: 'boolean' }).notNull().default(true),
	canDeleteOwn: integer('can_delete_own', { mode: 'boolean' }).notNull().default(false),
	canModerate: integer('can_moderate', { mode: 'boolean' }).notNull().default(false),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
}, (table) => ({
	uniqueBoardRole: unique().on(table.boardId, table.role)
}));

// Topic Subscriptions (Notification System)
export const topicSubscriptions = sqliteTable('topic_subscriptions', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	topicId: integer('topic_id').notNull().references(() => topics.id, { onDelete: 'cascade' }),
	userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
	notificationType: text('notification_type', { enum: ['immediate', 'daily', 'weekly', 'none'] }).notNull().default('immediate'),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
}, (table) => ({
	uniqueTopicUser: unique().on(table.topicId, table.userId)
}));

// Post Reactions (Like/Dislike System)
export const postReactions = sqliteTable('post_reactions', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	postId: integer('post_id').notNull().references(() => posts.id, { onDelete: 'cascade' }),
	userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
	reactionType: text('reaction_type', { enum: ['like', 'love', 'laugh', 'wow', 'sad', 'angry'] }).notNull(),
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
}, (table) => ({
	uniquePostUserReaction: unique().on(table.postId, table.userId, table.reactionType)
}));

// Moderation Actions (Audit Trail for Moderation)
export const moderationActions = sqliteTable('moderation_actions', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	targetType: text('target_type', { enum: ['topic', 'post', 'user'] }).notNull(),
	targetId: integer('target_id').notNull(),
	moderatorId: integer('moderator_id').notNull().references(() => users.id),
	actionType: text('action_type', { enum: ['pin', 'unpin', 'lock', 'unlock', 'archive', 'delete', 'edit', 'warn', 'suspend'] }).notNull(),
	reason: text('reason'),
	details: text('details'), // JSON for additional data
	createdAt: text('created_at').notNull().default(sql`CURRENT_TIMESTAMP`)
});

// Message Board Relations

export const boardsRelations = relations(boards, ({ many }) => ({
	topics: many(topics),
	permissions: many(boardPermissions)
}));

export const topicsRelations = relations(topics, ({ one, many }) => ({
	board: one(boards, {
		fields: [topics.boardId],
		references: [boards.id]
	}),
	author: one(users, {
		fields: [topics.authorId],
		references: [users.id]
	}),
	lastPost: one(posts, {
		fields: [topics.lastPostId],
		references: [posts.id]
	}),
	posts: many(posts),
	subscriptions: many(topicSubscriptions)
}));

export const postsRelations = relations(posts, ({ one, many }) => ({
	topic: one(topics, {
		fields: [posts.topicId],
		references: [topics.id]
	}),
	author: one(users, {
		fields: [posts.authorId],
		references: [users.id]
	}),
	parentPost: one(posts, {
		fields: [posts.parentPostId],
		references: [posts.id],
		relationName: 'postReplies'
	}),
	replies: many(posts, {
		relationName: 'postReplies'
	}),
	editedByUser: one(users, {
		fields: [posts.editedBy],
		references: [users.id],
		relationName: 'postEditor'
	}),
	deletedByUser: one(users, {
		fields: [posts.deletedBy],
		references: [users.id],
		relationName: 'postDeleter'
	}),
	reactions: many(postReactions)
}));

export const boardPermissionsRelations = relations(boardPermissions, ({ one }) => ({
	board: one(boards, {
		fields: [boardPermissions.boardId],
		references: [boards.id]
	})
}));

export const topicSubscriptionsRelations = relations(topicSubscriptions, ({ one }) => ({
	topic: one(topics, {
		fields: [topicSubscriptions.topicId],
		references: [topics.id]
	}),
	user: one(users, {
		fields: [topicSubscriptions.userId],
		references: [users.id]
	})
}));

export const postReactionsRelations = relations(postReactions, ({ one }) => ({
	post: one(posts, {
		fields: [postReactions.postId],
		references: [posts.id]
	}),
	user: one(users, {
		fields: [postReactions.userId],
		references: [users.id]
	})
}));

export const moderationActionsRelations = relations(moderationActions, ({ one }) => ({
	moderator: one(users, {
		fields: [moderationActions.moderatorId],
		references: [users.id]
	})
}));
