import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { boards, topics, posts, users } from '$lib/server/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';

// GET - Get board details and topics for public viewing
export const GET: RequestHandler = async ({ params, url }) => {
	try {
		const boardSlug = params.slug;
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = parseInt(url.searchParams.get('limit') || '20');
		const offset = (page - 1) * limit;

		// Get board details
		const [board] = await db
			.select({
				id: boards.id,
				name: boards.name,
				description: boards.description,
				slug: boards.slug,
				icon: boards.icon,
				color: boards.color
			})
			.from(boards)
			.where(and(eq(boards.slug, boardSlug), eq(boards.isActive, true)))
			.limit(1);

		if (!board) {
			return json({ 
				success: false, 
				error: 'Board not found' 
			}, { status: 404 });
		}

		// Get topics with pagination and author info
		const topicsWithDetails = await db
			.select({
				id: topics.id,
				title: topics.title,
				slug: topics.slug,
				description: topics.description,
				isPinned: topics.isPinned,
				isLocked: topics.isLocked,
				viewCount: topics.viewCount,
				postCount: topics.postCount,
				lastPostAt: topics.lastPostAt,
				createdAt: topics.createdAt,
				authorName: users.displayName,
				authorId: users.id
			})
			.from(topics)
			.leftJoin(users, eq(topics.authorId, users.id))
			.where(and(
				eq(topics.boardId, board.id),
				eq(topics.isArchived, false)
			))
			.orderBy(desc(topics.isPinned), desc(topics.lastPostAt), desc(topics.createdAt))
			.limit(limit)
			.offset(offset);

		// Get total count for pagination
		const [totalCount] = await db
			.select({ count: sql<number>`COUNT(*)` })
			.from(topics)
			.where(and(
				eq(topics.boardId, board.id),
				eq(topics.isArchived, false)
			));

		// Get board statistics
		const [boardStats] = await db
			.select({
				topicCount: sql<number>`COUNT(DISTINCT ${topics.id})`,
				postCount: sql<number>`COUNT(DISTINCT ${posts.id})`
			})
			.from(topics)
			.leftJoin(posts, and(eq(posts.topicId, topics.id), eq(posts.isDeleted, false)))
			.where(and(
				eq(topics.boardId, board.id),
				eq(topics.isArchived, false)
			));

		return json({
			success: true,
			data: {
				board: {
					...board,
					stats: {
						topicCount: boardStats?.topicCount || 0,
						postCount: boardStats?.postCount || 0
					}
				},
				topics: topicsWithDetails,
				pagination: {
					page,
					limit,
					total: totalCount?.count || 0,
					totalPages: Math.ceil((totalCount?.count || 0) / limit),
					hasNext: page * limit < (totalCount?.count || 0),
					hasPrev: page > 1
				}
			}
		});

	} catch (error) {
		console.error('Error fetching board topics:', error);
		return json({ 
			success: false, 
			error: 'Failed to load board topics' 
		}, { status: 500 });
	}
};
