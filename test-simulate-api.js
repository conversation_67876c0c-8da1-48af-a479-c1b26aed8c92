import fetch from 'node-fetch';

async function testAPI() {
  const baseUrl = 'http://localhost:5174';
  
  console.log('Testing Simulate Interactions API endpoints...\n');
  
  // Test 1: Content API (should work without auth for published content)
  try {
    console.log('1. Testing content API...');
    const newsResponse = await fetch(`${baseUrl}/api/admin/content/news?published=true&limit=5`);
    const newsResult = await newsResponse.json();
    console.log('News API Status:', newsResponse.status);
    console.log('News API Response:', newsResult.success ? `Success - ${newsResult.data?.length || 0} items` : `Error: ${newsResult.error}`);
    
    const galleryResponse = await fetch(`${baseUrl}/api/admin/content/gallery?published=true&limit=5`);
    const galleryResult = await galleryResponse.json();
    console.log('Gallery API Status:', galleryResponse.status);
    console.log('Gallery API Response:', galleryResult.success ? `Success - ${galleryResult.data?.length || 0} items` : `Error: ${galleryResult.error}`);
  } catch (error) {
    console.log('Content API Error:', error.message);
  }
  
  console.log('\n2. Testing admin endpoints (will fail without auth)...');
  
  // Test 2: Interaction Templates API
  try {
    const templatesResponse = await fetch(`${baseUrl}/api/admin/interaction-templates`);
    const templatesResult = await templatesResponse.json();
    console.log('Templates API Status:', templatesResponse.status);
    console.log('Templates API Response:', templatesResult.success ? `Success - ${templatesResult.data?.length || 0} templates` : `Error: ${templatesResult.error}`);
  } catch (error) {
    console.log('Templates API Error:', error.message);
  }
  
  // Test 3: Users API
  try {
    const usersResponse = await fetch(`${baseUrl}/api/admin/users?type=simulated&status=active&limit=100`);
    const usersResult = await usersResponse.json();
    console.log('Users API Status:', usersResponse.status);
    console.log('Users API Response:', usersResult.success ? `Success - ${usersResult.data?.users?.length || 0} users` : `Error: ${usersResult.error}`);
  } catch (error) {
    console.log('Users API Error:', error.message);
  }
  
  // Test 4: Simulate Interactions API
  try {
    const simulateResponse = await fetch(`${baseUrl}/api/admin/simulate-interactions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateId: 1,
        userIds: [2],
        contentType: 'news',
        contentId: 3,
        generateVariations: false,
        maxInteractions: 1
      })
    });
    const simulateResult = await simulateResponse.json();
    console.log('Simulate API Status:', simulateResponse.status);
    console.log('Simulate API Response:', simulateResult.success ? `Success - ${simulateResult.data?.totalInteractions || 0} interactions` : `Error: ${simulateResult.error}`);
  } catch (error) {
    console.log('Simulate API Error:', error.message);
  }
}

testAPI().catch(console.error);
