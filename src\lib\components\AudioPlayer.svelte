<script>
	import { onMount, onDestroy } from 'svelte';
	import { createEventDispatcher } from 'svelte';
	import { browser } from '$app/environment';

	const dispatch = createEventDispatcher();

	// Props
	export let audioUrl = '';
	export let audioTitle = '';
	export let audioDuration = 0;
	export let autoplay = false;
	export let showDownload = true;

	// State
	let audio;
	let isPlaying = false;
	let currentTime = 0;
	let duration = 0;
	let volume = 1;
	let isLoading = false;
	let error = null;
	let showVolumeSlider = false;

	// Format time in MM:SS format
	function formatTime(seconds) {
		if (!seconds || isNaN(seconds)) return '0:00';
		const mins = Math.floor(seconds / 60);
		const secs = Math.floor(seconds % 60);
		return `${mins}:${secs.toString().padStart(2, '0')}`;
	}

	// Handle play/pause
	function togglePlay() {
		if (!audio) return;
		
		if (isPlaying) {
			audio.pause();
		} else {
			audio.play().catch(err => {
				console.error('Error playing audio:', err);
				error = 'Failed to play audio';
			});
		}
	}

	// Handle seek
	function handleSeek(event) {
		if (!audio) return;
		const rect = event.currentTarget.getBoundingClientRect();
		const percent = (event.clientX - rect.left) / rect.width;
		audio.currentTime = percent * duration;
	}

	// Handle seek with keyboard
	function handleSeekKeydown(event) {
		if (!audio) return;

		switch (event.key) {
			case 'ArrowLeft':
				event.preventDefault();
				audio.currentTime = Math.max(0, audio.currentTime - 10);
				break;
			case 'ArrowRight':
				event.preventDefault();
				audio.currentTime = Math.min(duration, audio.currentTime + 10);
				break;
			case 'Home':
				event.preventDefault();
				audio.currentTime = 0;
				break;
			case 'End':
				event.preventDefault();
				audio.currentTime = duration;
				break;
		}
	}

	// Handle volume change
	function handleVolumeChange(event) {
		volume = parseFloat(event.target.value);
		if (audio) {
			audio.volume = volume;
		}
	}

	// Handle download
	function handleDownload() {
		if (browser && audioUrl) {
			const link = document.createElement('a');
			link.href = audioUrl;
			link.download = audioTitle || 'audio';
			link.click();
		}
	}

	// Keyboard shortcuts
	function handleKeydown(event) {
		if (event.target.tagName === 'INPUT') return;
		
		switch (event.key) {
			case ' ':
			case 'k':
				event.preventDefault();
				togglePlay();
				break;
			case 'ArrowLeft':
				event.preventDefault();
				if (audio) audio.currentTime = Math.max(0, audio.currentTime - 10);
				break;
			case 'ArrowRight':
				event.preventDefault();
				if (audio) audio.currentTime = Math.min(duration, audio.currentTime + 10);
				break;
			case 'm':
				event.preventDefault();
				if (audio) audio.muted = !audio.muted;
				break;
		}
	}

	onMount(() => {
		if (browser && audioUrl) {
			audio = new Audio(audioUrl);
			
			// Set initial volume
			audio.volume = volume;
			
			// Event listeners
			audio.addEventListener('loadstart', () => {
				isLoading = true;
				error = null;
			});
			
			audio.addEventListener('loadedmetadata', () => {
				duration = audio.duration || audioDuration || 0;
				isLoading = false;
			});
			
			audio.addEventListener('timeupdate', () => {
				currentTime = audio.currentTime;
			});
			
			audio.addEventListener('play', () => {
				isPlaying = true;
				dispatch('play');
			});
			
			audio.addEventListener('pause', () => {
				isPlaying = false;
				dispatch('pause');
			});
			
			audio.addEventListener('ended', () => {
				isPlaying = false;
				dispatch('ended');
			});
			
			audio.addEventListener('error', (e) => {
				console.error('Audio error:', e);
				error = 'Failed to load audio';
				isLoading = false;
			});

			// Auto-play if requested
			if (autoplay) {
				audio.play().catch(err => {
					console.error('Autoplay failed:', err);
				});
			}
		}

		// Add keyboard event listener
		if (browser) {
			document.addEventListener('keydown', handleKeydown);
		}
	});

	onDestroy(() => {
		if (audio) {
			audio.pause();
			audio.src = '';
		}
		if (browser) {
			document.removeEventListener('keydown', handleKeydown);
		}
	});
</script>

{#if audioUrl}
	<div class="audio-player" role="region" aria-label="Audio Player">
		{#if audioTitle}
			<div class="audio-title">
				<h4>{audioTitle}</h4>
			</div>
		{/if}

		<div class="player-controls">
			<!-- Play/Pause Button -->
			<button 
				class="play-button"
				on:click={togglePlay}
				disabled={isLoading || error}
				aria-label={isPlaying ? 'Pause' : 'Play'}
				title={isPlaying ? 'Pause (Space)' : 'Play (Space)'}
			>
				{#if isLoading}
					<span class="loading-spinner" aria-hidden="true">⟳</span>
				{:else if isPlaying}
					<span aria-hidden="true">⏸</span>
				{:else}
					<span aria-hidden="true">▶</span>
				{/if}
			</button>

			<!-- Progress Bar -->
			<div class="progress-container">
				<div
					class="progress-bar"
					on:click={handleSeek}
					on:keydown={handleSeekKeydown}
					role="slider"
					aria-label="Seek audio position"
					aria-valuemin="0"
					aria-valuemax={duration}
					aria-valuenow={currentTime}
					tabindex="0"
				>
					<div 
						class="progress-fill"
						style="width: {duration ? (currentTime / duration) * 100 : 0}%"
					></div>
				</div>
				
				<div class="time-display">
					<span class="current-time">{formatTime(currentTime)}</span>
					<span class="duration">{formatTime(duration)}</span>
				</div>
			</div>

			<!-- Volume Control -->
			<div class="volume-control">
				<button 
					class="volume-button"
					on:click={() => showVolumeSlider = !showVolumeSlider}
					aria-label="Volume control"
					title="Volume (M to mute)"
				>
					{#if volume === 0}
						<span aria-hidden="true">🔇</span>
					{:else if volume < 0.5}
						<span aria-hidden="true">🔉</span>
					{:else}
						<span aria-hidden="true">🔊</span>
					{/if}
				</button>
				
				{#if showVolumeSlider}
					<input
						type="range"
						class="volume-slider"
						min="0"
						max="1"
						step="0.1"
						bind:value={volume}
						on:input={handleVolumeChange}
						aria-label="Volume level"
					/>
				{/if}
			</div>

			<!-- Download Button -->
			{#if showDownload}
				<button 
					class="download-button"
					on:click={handleDownload}
					aria-label="Download audio file"
					title="Download audio file"
				>
					<span aria-hidden="true">⬇</span>
				</button>
			{/if}
		</div>

		{#if error}
			<div class="error-message" role="alert">
				{error}
			</div>
		{/if}
	</div>
{/if}

<style>
	.audio-player {
		background-color: var(--color-surface-secondary);
		border: 1px solid var(--color-border-primary);
		border-radius: 8px;
		padding: 1.5rem;
		margin: 1.5rem 0;
		color: var(--color-text-primary);
	}

	.audio-title {
		margin-bottom: 1rem;
		text-align: center;
	}

	.audio-title h4 {
		margin: 0;
		font-size: 1.1rem;
		color: var(--color-text-primary);
	}

	.player-controls {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.play-button {
		width: 3rem;
		height: 3rem;
		border-radius: 50%;
		border: none;
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
		font-size: 1.2rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: var(--transition-theme);
		flex-shrink: 0;
	}

	.play-button:hover:not(:disabled) {
		background-color: var(--color-interactive-primary-hover);
		transform: scale(1.05);
	}

	.play-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.loading-spinner {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.progress-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.progress-bar {
		height: 6px;
		background-color: var(--color-surface-tertiary);
		border-radius: 3px;
		cursor: pointer;
		position: relative;
		border: 1px solid var(--color-border-secondary);
	}

	.progress-bar:focus {
		outline: 2px solid var(--color-border-focus);
		outline-offset: 2px;
	}

	.progress-fill {
		height: 100%;
		background-color: var(--color-interactive-primary);
		border-radius: 3px;
		transition: width 0.1s ease;
	}

	.time-display {
		display: flex;
		justify-content: space-between;
		font-size: 0.85rem;
		color: var(--color-text-secondary);
		font-family: monospace;
	}

	.volume-control {
		position: relative;
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.volume-button {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 4px;
		border: 1px solid var(--color-border-primary);
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: var(--transition-theme);
	}

	.volume-button:hover {
		background-color: var(--color-surface-tertiary);
	}

	.volume-slider {
		width: 80px;
		height: 4px;
		background-color: var(--color-surface-tertiary);
		border-radius: 2px;
		outline: none;
		cursor: pointer;
		-webkit-appearance: none;
		appearance: none;
	}

	.volume-slider::-webkit-slider-thumb {
		-webkit-appearance: none;
		appearance: none;
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background-color: var(--color-interactive-primary);
		cursor: pointer;
	}

	.volume-slider::-moz-range-thumb {
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background-color: var(--color-interactive-primary);
		cursor: pointer;
		border: none;
	}

	.download-button {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 4px;
		border: 1px solid var(--color-border-primary);
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: var(--transition-theme);
	}

	.download-button:hover {
		background-color: var(--color-surface-tertiary);
	}

	.error-message {
		margin-top: 1rem;
		padding: 0.75rem;
		background-color: var(--color-error-bg);
		color: var(--color-error);
		border: 1px solid var(--color-error-border);
		border-radius: 4px;
		text-align: center;
		font-size: 0.9rem;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.player-controls {
			flex-wrap: wrap;
			gap: 0.75rem;
		}

		.progress-container {
			order: 3;
			width: 100%;
		}

		.volume-control {
			order: 2;
		}

		.download-button {
			order: 4;
		}

		.volume-slider {
			width: 60px;
		}
	}

	@media (max-width: 480px) {
		.audio-player {
			padding: 1rem;
		}

		.play-button {
			width: 2.5rem;
			height: 2.5rem;
			font-size: 1rem;
		}

		.volume-button,
		.download-button {
			width: 2rem;
			height: 2rem;
		}

		.time-display {
			font-size: 0.8rem;
		}
	}
</style>
