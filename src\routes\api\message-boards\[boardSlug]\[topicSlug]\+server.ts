import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { boards, topics, posts, users } from '$lib/server/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';
import { getBoardPermissions, getUserRole } from '$lib/server/permissions';

// GET - Get topic details and posts for public viewing
export const GET: RequestHandler = async ({ params, url, locals }) => {
	try {
		const boardSlug = params.boardSlug;
		const topicSlug = params.topicSlug;
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = parseInt(url.searchParams.get('limit') || '20');
		const offset = (page - 1) * limit;

		// Get board details
		const [board] = await db
			.select({
				id: boards.id,
				name: boards.name,
				slug: boards.slug,
				isActive: boards.isActive
			})
			.from(boards)
			.where(and(eq(boards.slug, boardSlug), eq(boards.isActive, true)))
			.limit(1);

		if (!board) {
			return json({ 
				success: false, 
				error: 'Board not found' 
			}, { status: 404 });
		}

		// Get topic details
		const [topic] = await db
			.select({
				id: topics.id,
				title: topics.title,
				description: topics.description,
				slug: topics.slug,
				isPinned: topics.isPinned,
				isLocked: topics.isLocked,
				isArchived: topics.isArchived,
				viewCount: topics.viewCount,
				postCount: topics.postCount,
				createdAt: topics.createdAt,
				authorName: users.displayName,
				authorId: users.id
			})
			.from(topics)
			.leftJoin(users, eq(topics.authorId, users.id))
			.where(and(
				eq(topics.boardId, board.id),
				eq(topics.slug, topicSlug),
				eq(topics.isArchived, false)
			))
			.limit(1);

		if (!topic) {
			return json({ 
				success: false, 
				error: 'Topic not found' 
			}, { status: 404 });
		}

		// Get posts with pagination and author info
		const postsWithDetails = await db
			.select({
				id: posts.id,
				content: posts.content,
				createdAt: posts.createdAt,
				updatedAt: posts.updatedAt,
				authorName: users.displayName,
				authorId: users.id,
				authorRole: users.role
			})
			.from(posts)
			.leftJoin(users, eq(posts.authorId, users.id))
			.where(and(
				eq(posts.topicId, topic.id),
				eq(posts.isDeleted, false)
			))
			.orderBy(posts.createdAt)
			.limit(limit)
			.offset(offset);

		// Get total post count for pagination
		const [totalCount] = await db
			.select({ count: sql<number>`COUNT(*)` })
			.from(posts)
			.where(and(
				eq(posts.topicId, topic.id),
				eq(posts.isDeleted, false)
			));

		// Increment view count
		await db
			.update(topics)
			.set({ 
				viewCount: topic.viewCount + 1,
				updatedAt: new Date().toISOString()
			})
			.where(eq(topics.id, topic.id));

		// Get user permissions for this board
		const userRole = getUserRole(locals.user);
		const permissions = await getBoardPermissions(board.id, userRole);

		return json({
			success: true,
			data: {
				board: {
					id: board.id,
					name: board.name,
					slug: board.slug
				},
				topic: {
					...topic,
					viewCount: topic.viewCount + 1 // Return updated view count
				},
				posts: postsWithDetails,
				permissions,
				user: locals.user ? {
					id: locals.user.id,
					username: locals.user.username,
					displayName: locals.user.displayName,
					role: locals.user.role
				} : null,
				pagination: {
					page,
					limit,
					total: totalCount?.count || 0,
					totalPages: Math.ceil((totalCount?.count || 0) / limit),
					hasNext: page * limit < (totalCount?.count || 0),
					hasPrev: page > 1
				}
			}
		});

	} catch (error) {
		console.error('Error fetching topic:', error);
		return json({ 
			success: false, 
			error: 'Failed to load topic' 
		}, { status: 500 });
	}
};
