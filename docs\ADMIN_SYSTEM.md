# FWFC Admin System Documentation

## Overview

The FWFC Admin System provides comprehensive tools for managing users, content, and simulated interactions to create a safe, controlled online environment. This system is designed to help administrators maintain authentic-looking community engagement while ensuring content quality and user safety.

## Key Features

### 1. Enhanced User Management
- **Comprehensive User Profiles**: Extended user profiles with bio, location, interests, and avatar support
- **User Status Management**: Active, inactive, and suspended user states
- **Simulated User Support**: Special accounts for generating authentic-looking interactions
- **Advanced Search & Filtering**: Search by username, email, role, status, and user type
- **Bulk Operations**: Efficient management of multiple users simultaneously
- **Activity Tracking**: Monitor user engagement and last active timestamps

### 2. Content Posting as Users
- **Post as Any User**: Create content that appears to be authored by specific users
- **Multiple Content Types**: Support for news articles, gallery items, comments, and messages
- **Content Authorship Tracking**: Maintain records of actual vs. display authors
- **Rich Content Support**: Full text editing, image uploads, and media management
- **Publishing Controls**: Immediate publishing or draft saving options

### 3. Content Scheduling System
- **Flexible Scheduling**: Schedule content to be published at specific times
- **Automated Publishing**: Background job system for automatic content publication
- **Schedule Management**: View, edit, cancel, and retry scheduled content
- **Bulk Operations**: Manage multiple scheduled items efficiently
- **Error Handling**: Comprehensive error tracking and retry mechanisms

### 4. Audit Logging & Security
- **Comprehensive Audit Trail**: Track all administrative actions with detailed logs
- **Security Monitoring**: Monitor admin access patterns and suspicious activities
- **Data Export**: Export audit logs in CSV and JSON formats for analysis
- **Risk Assessment**: Categorize actions by risk level (low, medium, high)
- **IP Tracking**: Record IP addresses for all administrative actions



## System Architecture

### Database Schema

The admin system extends the core database schema with several new tables:

- `users` - Enhanced with profile fields, status, and simulation support
- `scheduledContent` - Manages content scheduled for future publication
- `contentAuthorship` - Tracks actual vs. display authors for transparency
- `auditLogs` - Comprehensive logging of all administrative actions
- `interactionTemplates` - Reusable templates for generating user interactions

### Security Model

- **Role-Based Access Control**: Admin-only access to all administrative functions
- **Action Logging**: Every administrative action is logged with full context
- **IP Tracking**: All actions are associated with IP addresses for security
- **Session Management**: Secure session handling with proper authentication
- **Data Validation**: Comprehensive input validation and sanitization

### Background Jobs

The system includes a background job scheduler for:
- **Automatic Content Publishing**: Publishes scheduled content at the right time
- **User Activity Simulation**: Generates scheduled user interactions
- **System Maintenance**: Cleanup and optimization tasks
- **Error Recovery**: Retry failed operations automatically

## User Interface

### Admin Dashboard
- **Overview Statistics**: Key metrics and system status
- **Quick Actions**: Common administrative tasks
- **Recent Activity**: Latest administrative actions and system events
- **System Health**: Monitor background jobs and system performance

### User Management Interface
- **Advanced Search**: Multi-criteria search with real-time filtering
- **User Profiles**: Comprehensive user editing with all profile fields
- **Bulk Operations**: Select and manage multiple users efficiently
- **Activity Monitoring**: Track user engagement and behavior patterns

### Content Management
- **Post as User**: Intuitive interface for creating content as any user
- **Content Scheduling**: Visual calendar and scheduling interface
- **Template Management**: Create and manage interaction templates
- **Media Integration**: Seamless image and media management

### Audit & Security
- **Audit Log Viewer**: Searchable, filterable audit trail
- **Security Dashboard**: Monitor system security and access patterns
- **Export Tools**: Generate reports and export data for analysis
- **Risk Assessment**: Visual indicators for high-risk actions

## API Endpoints

### User Management
- `GET /api/admin/users` - List users with filtering and pagination
- `POST /api/admin/users` - Create new user accounts
- `PUT /api/admin/users/[id]` - Update user profiles and settings
- `DELETE /api/admin/users/[id]` - Delete user accounts
- `PATCH /api/admin/users/bulk` - Bulk user operations

### Content Management
- `POST /api/admin/post-as-user` - Create content as specific users
- `GET /api/admin/content/[type]` - Retrieve content by type
- `GET /api/admin/scheduled-content` - List scheduled content
- `POST /api/admin/scheduled-content/[id]/publish` - Publish scheduled content immediately



### Audit & Security
- `GET /api/admin/audit-logs` - Retrieve audit logs with filtering
- `GET /api/admin/audit-logs/export` - Export audit logs in various formats

## Configuration

### Environment Variables
```env
# Database Configuration
DATABASE_URL=postgresql://...

# Security Settings
ADMIN_SESSION_SECRET=your-secret-key
ADMIN_SESSION_TIMEOUT=3600

# Background Jobs
SCHEDULER_ENABLED=true
SCHEDULER_INTERVAL=60000

# Audit Logging
AUDIT_LOG_RETENTION_DAYS=365
AUDIT_LOG_EXPORT_ENABLED=true
```

### Feature Flags
```javascript
// Enable/disable specific admin features
const adminConfig = {
  userSimulation: true,
  contentScheduling: true,
  auditLogging: true,
  bulkOperations: true,
  interactionTemplates: true
};
```

## Security Considerations

### Access Control
- All admin functions require authenticated admin users
- Role-based permissions prevent unauthorized access
- Session management with secure cookies and CSRF protection

### Data Protection
- Sensitive data is encrypted at rest and in transit
- Audit logs include data change tracking
- User privacy is maintained while enabling administrative oversight

### Monitoring & Alerting
- Failed login attempts are logged and monitored
- Unusual administrative activity triggers alerts
- System health monitoring with automated notifications

## Best Practices

### User Management
1. **Regular Audits**: Review user accounts and permissions regularly
2. **Simulated User Guidelines**: Maintain clear policies for simulated accounts
3. **Activity Monitoring**: Track user engagement patterns for insights
4. **Data Cleanup**: Regularly archive or remove inactive accounts

### Content Management
1. **Content Quality**: Maintain high standards for all published content
2. **Scheduling Strategy**: Plan content publication for optimal engagement
3. **Template Maintenance**: Keep interaction templates current and relevant
4. **Authorship Transparency**: Maintain clear records of content origins

### Security & Compliance
1. **Audit Log Review**: Regularly review audit logs for anomalies
2. **Access Management**: Limit admin access to necessary personnel only
3. **Data Retention**: Follow data retention policies for audit logs
4. **Incident Response**: Have procedures for security incidents

## Troubleshooting

### Common Issues

#### Scheduled Content Not Publishing
- Check background job scheduler status
- Verify user account status and permissions
- Review error logs for specific failure reasons

#### User Creation Failures
- Validate required fields and data formats
- Check for duplicate usernames or email addresses
- Verify database connectivity and permissions

#### Audit Log Export Issues
- Check file permissions and disk space
- Verify export format compatibility
- Review filter criteria for data availability

### Performance Optimization
- Use pagination for large data sets
- Implement caching for frequently accessed data
- Monitor database query performance
- Optimize background job scheduling

## Support & Maintenance

### Regular Maintenance Tasks
- Database optimization and cleanup
- Audit log archival and rotation
- User account review and cleanup
- System performance monitoring

### Monitoring Metrics
- User account creation and activity rates
- Content publication and engagement metrics
- System performance and error rates
- Security event frequency and patterns

### Backup & Recovery
- Regular database backups with point-in-time recovery
- Audit log backup and archival procedures
- System configuration backup and versioning
- Disaster recovery testing and procedures

## Implementation Details

### Database Migrations
The admin system requires several database schema updates. Run migrations in order:

```sql
-- Add user profile fields
ALTER TABLE users ADD COLUMN bio TEXT;
ALTER TABLE users ADD COLUMN avatar_url TEXT;
ALTER TABLE users ADD COLUMN location TEXT;
ALTER TABLE users ADD COLUMN website TEXT;
ALTER TABLE users ADD COLUMN birth_date DATE;
ALTER TABLE users ADD COLUMN interests JSONB DEFAULT '[]';
ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'active';
ALTER TABLE users ADD COLUMN is_simulated BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN simulated_personality JSONB;
ALTER TABLE users ADD COLUMN last_active_at TIMESTAMP;

-- Create scheduled content table
CREATE TABLE scheduled_content (
  id SERIAL PRIMARY KEY,
  content_type VARCHAR(50) NOT NULL,
  content_data JSONB NOT NULL,
  as_user_id INTEGER REFERENCES users(id),
  scheduled_for TIMESTAMP NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  published_at TIMESTAMP,
  error_message TEXT,
  created_by_user_id INTEGER REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create content authorship tracking table
CREATE TABLE content_authorship (
  id SERIAL PRIMARY KEY,
  content_type VARCHAR(50) NOT NULL,
  content_id INTEGER NOT NULL,
  actual_author_id INTEGER REFERENCES users(id),
  display_author_id INTEGER REFERENCES users(id),
  is_simulated BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create audit logs table
CREATE TABLE audit_logs (
  id SERIAL PRIMARY KEY,
  admin_user_id INTEGER REFERENCES users(id),
  action VARCHAR(100) NOT NULL,
  target_type VARCHAR(50),
  target_id INTEGER,
  target_user_id INTEGER REFERENCES users(id),
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);



-- Create indexes for performance
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_is_simulated ON users(is_simulated);
CREATE INDEX idx_users_last_active ON users(last_active_at);
CREATE INDEX idx_scheduled_content_status ON scheduled_content(status);
CREATE INDEX idx_scheduled_content_scheduled_for ON scheduled_content(scheduled_for);
CREATE INDEX idx_audit_logs_admin_user ON audit_logs(admin_user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_content_authorship_content ON content_authorship(content_type, content_id);
```

### Component Architecture
The admin system follows a modular component architecture:

```
src/routes/admin/
├── +layout.svelte              # Admin layout with navigation
├── +page.svelte                # Admin dashboard
├── users/
│   ├── +page.svelte           # User management interface
│   └── +page.server.ts        # User data loading
├── post-as-user/
│   ├── +page.svelte           # Content posting interface
│   └── +page.server.ts        # Content posting logic
├── scheduled-content/
│   ├── +page.svelte           # Scheduling management
│   └── +page.server.ts        # Scheduling data

└── audit-logs/
    ├── +page.svelte           # Audit log viewer
    └── +page.server.ts        # Audit data loading
```

### API Structure
```
src/routes/api/admin/
├── users/
│   ├── +server.ts             # User CRUD operations
│   └── [id]/
│       └── +server.ts         # Individual user operations
├── post-as-user/
│   └── +server.ts             # Content posting API
├── scheduled-content/
│   ├── +server.ts             # Scheduling CRUD
│   ├── [id]/
│   │   └── +server.ts         # Individual schedule operations
│   └── [id]/publish/
│       └── +server.ts         # Immediate publishing

├── content/
│   └── [type]/
│       └── +server.ts         # Content retrieval by type
└── audit-logs/
    ├── +server.ts             # Audit log retrieval
    └── export/
        └── +server.ts         # Audit log export
```
