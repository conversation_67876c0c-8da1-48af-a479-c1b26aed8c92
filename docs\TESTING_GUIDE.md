# FWFC Admin System Testing Guide

## Overview

This guide covers comprehensive testing strategies for the FWFC Admin System, including unit tests, integration tests, and end-to-end testing procedures.

## Test Structure

### Test Organization
```
src/tests/
├── admin/
│   ├── user-management.test.ts      # User management system tests
│   ├── content-posting.test.ts      # Content posting functionality
│   ├── content-scheduling.test.ts   # Scheduling system tests
│   ├── audit-logging.test.ts        # Audit and security tests
│   └── interaction-simulation.test.ts # Interaction generation tests
├── api/
│   ├── admin-users.test.ts          # User API endpoint tests
│   ├── admin-content.test.ts        # Content API tests
│   └── admin-security.test.ts       # Security and auth tests
└── e2e/
    ├── admin-workflow.test.ts       # End-to-end admin workflows
    └── user-simulation.test.ts      # Complete simulation workflows
```

## Running Tests

### Prerequisites
```bash
# Install test dependencies
npm install --save-dev vitest @testing-library/svelte jsdom

# Set up test database
createdb fwfc_test
export DATABASE_URL="postgresql://localhost/fwfc_test"
```

### Test Commands
```bash
# Run all tests
npm run test

# Run specific test suites
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests
npm run test:e2e          # End-to-end tests

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm run test src/tests/admin/user-management.test.ts
```

## Test Categories

### 1. Unit Tests

#### User Management Tests
- **User Profile Management**: Test enhanced profile fields, validation, and updates
- **Simulated User Features**: Test personality settings and simulation flags
- **User Status Management**: Test status transitions and validation
- **Search and Filtering**: Test query building and result filtering
- **Data Validation**: Test input validation and error handling

```typescript
// Example test structure
describe('Enhanced User Management System', () => {
  describe('User Profile Management', () => {
    it('should create user with enhanced profile fields', async () => {
      // Test implementation
    });
    
    it('should update user profile fields', async () => {
      // Test implementation
    });
  });
});
```

#### Content Posting Tests
- **Content Creation**: Test posting as different users
- **Authorship Tracking**: Test actual vs display author recording
- **Content Types**: Test news, gallery, comments, and messages
- **Validation**: Test required fields and data formats
- **Activity Updates**: Test user activity timestamp updates

#### Scheduling System Tests
- **Schedule Creation**: Test content scheduling with various dates
- **Status Management**: Test pending, published, failed, cancelled states
- **Bulk Operations**: Test multiple item management
- **Error Handling**: Test failure scenarios and recovery
- **Data Integrity**: Test referential integrity and constraints

### 2. Integration Tests

#### API Endpoint Tests
Test all admin API endpoints with proper authentication and authorization:

```typescript
describe('Admin API Endpoints', () => {
  beforeEach(async () => {
    // Set up authenticated admin session
  });

  describe('User Management API', () => {
    it('should require admin authentication', async () => {
      // Test unauthorized access
    });
    
    it('should create user with valid data', async () => {
      // Test successful user creation
    });
  });
});
```

#### Database Integration Tests
- **Schema Validation**: Test database schema and constraints
- **Transaction Handling**: Test rollback scenarios
- **Performance**: Test query performance with large datasets
- **Concurrency**: Test concurrent operations

#### Background Job Tests
- **Scheduler Functionality**: Test content publishing automation
- **Error Recovery**: Test retry mechanisms
- **Performance**: Test job processing under load

### 3. End-to-End Tests

#### Complete Admin Workflows
Test entire admin workflows from UI to database:

```typescript
describe('Admin Workflow E2E', () => {
  it('should complete user creation workflow', async () => {
    // 1. Navigate to user management
    // 2. Fill out user creation form
    // 3. Submit and verify creation
    // 4. Check database state
    // 5. Verify audit log entry
  });
  
  it('should complete content posting workflow', async () => {
    // 1. Navigate to post-as-user interface
    // 2. Select user and content type
    // 3. Create content
    // 4. Verify publication
    // 5. Check authorship tracking
  });
});
```



## Test Data Management

### Test Database Setup
```sql
-- Create test-specific data
INSERT INTO users (username, display_name, email, role, is_simulated) VALUES
('testadmin', 'Test Admin', '<EMAIL>', 'admin', false),
('testuser', 'Test User', '<EMAIL>', 'user', false),
('simulated1', 'Simulated User 1', '<EMAIL>', 'user', true);

-- Create test content
INSERT INTO news (title, content, author_id, published) VALUES
('Test Article', 'Test content for interactions', 1, true);
```

### Data Cleanup
```typescript
// Automated cleanup after each test
afterEach(async () => {
  await db.delete(auditLogs).where(eq(auditLogs.adminUserId, testAdminId));
  await db.delete(scheduledContent).where(eq(scheduledContent.createdByUserId, testAdminId));
  await db.delete(users).where(eq(users.email, '<EMAIL>'));
});
```

## Performance Testing

### Load Testing
Test system performance under realistic loads:

```typescript
describe('Performance Tests', () => {
  it('should handle bulk user operations efficiently', async () => {
    const startTime = Date.now();
    
    // Create 1000 users
    const users = Array.from({ length: 1000 }, (_, i) => ({
      username: `user${i}`,
      email: `user${i}@test.com`,
      // ... other fields
    }));
    
    await db.insert(users).values(users);
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(5000); // Should complete in under 5 seconds
  });
});
```

### Memory Testing
Monitor memory usage during intensive operations:

```typescript
it('should not leak memory during bulk operations', async () => {
  const initialMemory = process.memoryUsage().heapUsed;
  
  // Perform memory-intensive operations
  for (let i = 0; i < 100; i++) {
    await performBulkOperation();
    if (global.gc) global.gc(); // Force garbage collection if available
  }
  
  const finalMemory = process.memoryUsage().heapUsed;
  const memoryIncrease = finalMemory - initialMemory;
  
  expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB increase
});
```

## Security Testing

### Authentication Tests
```typescript
describe('Security Tests', () => {
  it('should reject unauthorized admin access', async () => {
    const response = await fetch('/api/admin/users', {
      headers: { 'Authorization': 'Bearer invalid-token' }
    });
    
    expect(response.status).toBe(403);
  });
  
  it('should validate admin role requirements', async () => {
    // Test with regular user token
    const response = await fetch('/api/admin/users', {
      headers: { 'Authorization': `Bearer ${regularUserToken}` }
    });
    
    expect(response.status).toBe(403);
  });
});
```

### Input Validation Tests
```typescript
describe('Input Validation', () => {
  it('should sanitize user input', async () => {
    const maliciousInput = '<script>alert("xss")</script>';
    
    const response = await fetch('/api/admin/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: maliciousInput,
        email: '<EMAIL>'
      })
    });
    
    expect(response.status).toBe(400);
  });
});
```

## Accessibility Testing

### Screen Reader Compatibility
```typescript
describe('Accessibility Tests', () => {
  it('should have proper ARIA labels', async () => {
    render(UserManagementPage);
    
    expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
    expect(screen.getByRole('table')).toHaveAttribute('aria-label');
  });
  
  it('should support keyboard navigation', async () => {
    render(UserManagementPage);
    
    const firstButton = screen.getByRole('button', { name: /create user/i });
    firstButton.focus();
    
    fireEvent.keyDown(firstButton, { key: 'Tab' });
    
    expect(document.activeElement).not.toBe(firstButton);
  });
});
```

## Continuous Integration

### GitHub Actions Configuration
```yaml
name: Admin System Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: fwfc_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run database migrations
        run: npm run db:migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/fwfc_test
          
      - name: Run tests
        run: npm run test:coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/fwfc_test
          
      - name: Upload coverage reports
        uses: codecov/codecov-action@v1
```

## Test Coverage Goals

### Coverage Targets
- **Unit Tests**: 90%+ code coverage
- **Integration Tests**: 80%+ API endpoint coverage
- **E2E Tests**: 70%+ critical workflow coverage

### Coverage Reporting
```bash
# Generate coverage report
npm run test:coverage

# View coverage report
open coverage/index.html

# Check coverage thresholds
npm run test:coverage:check
```

## Debugging Tests

### Common Issues and Solutions

#### Database Connection Issues
```typescript
// Ensure proper test database setup
beforeAll(async () => {
  await db.migrate.latest();
  await db.seed.run();
});

afterAll(async () => {
  await db.destroy();
});
```

#### Async Test Issues
```typescript
// Use proper async/await patterns
it('should handle async operations', async () => {
  const result = await asyncOperation();
  expect(result).toBeDefined();
});
```

#### Mock and Stub Management
```typescript
// Clean up mocks between tests
afterEach(() => {
  vi.clearAllMocks();
  vi.restoreAllMocks();
});
```

## Best Practices

### Test Writing Guidelines
1. **Descriptive Names**: Use clear, descriptive test names
2. **Single Responsibility**: Each test should test one specific behavior
3. **Arrange-Act-Assert**: Follow the AAA pattern for test structure
4. **Independent Tests**: Tests should not depend on each other
5. **Realistic Data**: Use realistic test data that mirrors production

### Performance Considerations
1. **Parallel Execution**: Run tests in parallel when possible
2. **Database Optimization**: Use transactions for test isolation
3. **Resource Cleanup**: Always clean up test resources
4. **Selective Testing**: Use test tags for selective test execution

### Maintenance
1. **Regular Updates**: Keep tests updated with code changes
2. **Flaky Test Management**: Identify and fix unreliable tests
3. **Coverage Monitoring**: Monitor and maintain test coverage
4. **Documentation**: Keep test documentation current
