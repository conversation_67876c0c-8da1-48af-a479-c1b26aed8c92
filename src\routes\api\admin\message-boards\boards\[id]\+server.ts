import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { boards, topics, posts, auditLogs } from '$lib/server/db/schema';
import { eq, and, ne, sql } from 'drizzle-orm';

// GET - Get specific board
export const GET: RequestHandler = async ({ params, locals }) => {
	try {
		// Check authentication
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ success: false, error: 'Unauthorized' }, { status: 401 });
		}

		const boardId = parseInt(params.id);
		if (isNaN(boardId)) {
			return json({ success: false, error: 'Invalid board ID' }, { status: 400 });
		}

		// Get board
		const [board] = await db
			.select()
			.from(boards)
			.where(eq(boards.id, boardId))
			.limit(1);

		if (!board) {
			return json({ success: false, error: 'Board not found' }, { status: 404 });
		}

		return json({
			success: true,
			data: board
		});

	} catch (error) {
		console.error('Error fetching board:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};

// PUT - Update board
export const PUT: RequestHandler = async ({ params, request, locals, getClientAddress }) => {
	try {
		// Check authentication
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ success: false, error: 'Unauthorized' }, { status: 401 });
		}

		const boardId = parseInt(params.id);
		if (isNaN(boardId)) {
			return json({ success: false, error: 'Invalid board ID' }, { status: 400 });
		}

		const body = await request.json();
		const { name, description, slug, icon, color, position, isActive } = body;

		// Validate required fields
		if (!name || !slug) {
			return json({ success: false, error: 'Name and slug are required' }, { status: 400 });
		}

		// Validate slug format
		const slugRegex = /^[a-z0-9-]+$/;
		if (!slugRegex.test(slug)) {
			return json({ 
				success: false, 
				error: 'Slug must contain only lowercase letters, numbers, and hyphens' 
			}, { status: 400 });
		}

		// Check if board exists
		const [existingBoard] = await db
			.select()
			.from(boards)
			.where(eq(boards.id, boardId))
			.limit(1);

		if (!existingBoard) {
			return json({ success: false, error: 'Board not found' }, { status: 404 });
		}

		// Check if slug already exists (excluding current board)
		const duplicateBoard = await db
			.select({ id: boards.id })
			.from(boards)
			.where(and(eq(boards.slug, slug), ne(boards.id, boardId)))
			.limit(1);

		if (duplicateBoard.length > 0) {
			return json({ success: false, error: 'A board with this slug already exists' }, { status: 400 });
		}

		// Update the board
		const currentTime = new Date().toISOString();
		const [updatedBoard] = await db
			.update(boards)
			.set({
				name: name.trim(),
				description: description?.trim() || null,
				slug: slug.trim(),
				icon: icon?.trim() || null,
				color: color?.trim() || '#3B82F6',
				position: position || 0,
				isActive: isActive !== false,
				updatedAt: currentTime
			})
			.where(eq(boards.id, boardId))
			.returning();

		// Log the action
		await db
			.insert(auditLogs)
			.values({
				adminId: locals.user.id,
				action: 'update_board',
				targetType: 'board',
				targetId: boardId,
				details: JSON.stringify({
					boardName: name,
					boardSlug: slug,
					changes: {
						name: existingBoard.name !== name ? { from: existingBoard.name, to: name } : undefined,
						slug: existingBoard.slug !== slug ? { from: existingBoard.slug, to: slug } : undefined,
						isActive: existingBoard.isActive !== isActive ? { from: existingBoard.isActive, to: isActive } : undefined
					}
				}),
				ipAddress: getClientAddress(),
				userAgent: request.headers.get('user-agent') || '',
				riskLevel: 'low',
				createdAt: currentTime
			});

		return json({
			success: true,
			message: 'Board updated successfully',
			data: updatedBoard
		});

	} catch (error) {
		console.error('Error updating board:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};

// DELETE - Delete board
export const DELETE: RequestHandler = async ({ params, locals, getClientAddress, request }) => {
	try {
		// Check authentication
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ success: false, error: 'Unauthorized' }, { status: 401 });
		}

		const boardId = parseInt(params.id);
		if (isNaN(boardId)) {
			return json({ success: false, error: 'Invalid board ID' }, { status: 400 });
		}

		// Check if board exists
		const [existingBoard] = await db
			.select()
			.from(boards)
			.where(eq(boards.id, boardId))
			.limit(1);

		if (!existingBoard) {
			return json({ success: false, error: 'Board not found' }, { status: 404 });
		}

		// Check if board has topics
		const topicCount = await db
			.select({ count: sql<number>`COUNT(*)` })
			.from(topics)
			.where(eq(topics.boardId, boardId));

		if (topicCount[0]?.count > 0) {
			return json({ 
				success: false, 
				error: `Cannot delete board with ${topicCount[0].count} topics. Please move or delete all topics first.` 
			}, { status: 400 });
		}

		// Delete the board
		await db
			.delete(boards)
			.where(eq(boards.id, boardId));

		// Log the action
		const currentTime = new Date().toISOString();
		await db
			.insert(auditLogs)
			.values({
				adminId: locals.user.id,
				action: 'delete_board',
				targetType: 'board',
				targetId: boardId,
				details: JSON.stringify({
					boardName: existingBoard.name,
					boardSlug: existingBoard.slug
				}),
				ipAddress: getClientAddress(),
				userAgent: request.headers.get('user-agent') || '',
				riskLevel: 'medium',
				createdAt: currentTime
			});

		return json({
			success: true,
			message: 'Board deleted successfully'
		});

	} catch (error) {
		console.error('Error deleting board:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};
