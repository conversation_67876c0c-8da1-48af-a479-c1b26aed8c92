import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { news } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// GET /api/news/[id] - Get a specific news article
export const GET: RequestHandler = async ({ params }) => {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }

    // Fetch the news article from the database
    const article = await db.select()
      .from(news)
      .where(eq(news.id, id))
      .limit(1);

    if (article.length === 0) {
      return json({
        success: false,
        error: 'News article not found'
      }, { status: 404 });
    }

    return json({
      success: true,
      data: article[0]
    });
  } catch (error) {
    console.error('Error fetching news article:', error);
    return json({
      success: false,
      error: 'Failed to fetch news article'
    }, { status: 500 });
  }
};

// PUT /api/news/[id] - Update a news article (admin only)
export const PUT: RequestHandler = async ({ params, request, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }

    const body = await request.json();

    // Update the news article in the database
    const result = await db.update(news)
      .set({
        title: body.title,
        content: body.content,
        imageUrl: body.imageUrl,
        audioUrl: body.audioUrl,
        audioTitle: body.audioTitle,
        audioDuration: body.audioDuration,
        audioType: body.audioType,
        audioSize: body.audioSize,
        published: body.published,
        updatedAt: new Date().toISOString()
      })
      .where(eq(news.id, id))
      .returning();

    if (result.length === 0) {
      return json({
        success: false,
        error: 'News article not found'
      }, { status: 404 });
    }

    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('Error updating news article:', error);
    return json({
      success: false,
      error: 'Failed to update news article'
    }, { status: 500 });
  }
};

// DELETE /api/news/[id] - Delete a news article (admin only)
export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated and has admin role
  if (!locals.user || locals.user.role !== 'admin') {
    return json({
      success: false,
      error: 'Unauthorized'
    }, { status: 401 });
  }

  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return json({
        success: false,
        error: 'Invalid ID'
      }, { status: 400 });
    }

    // Delete the news article from the database
    const result = await db.delete(news)
      .where(eq(news.id, id))
      .returning();

    if (result.length === 0) {
      return json({
        success: false,
        error: 'News article not found'
      }, { status: 404 });
    }

    return json({
      success: true,
      data: result[0]
    });
  } catch (error) {
    console.error('Error deleting news article:', error);
    return json({
      success: false,
      error: 'Failed to delete news article'
    }, { status: 500 });
  }
};
