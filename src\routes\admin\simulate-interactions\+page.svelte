<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;
	// Use data to suppress unused warning
	data;

	// Simple state variables - no reactive getters or complex dependencies
	let simulatedUsers: any[] = [];
	let newsItems: any[] = [];
	let galleryItems: any[] = [];
	let isLoading = false;
	let error = '';
	let successMessage = '';

	// Form state - direct variables, no reactive dependencies
	let selectedUserIds: number[] = [];
	let selectedContentType = 'news';
	let selectedContentId: number | null = null;
	let customMessage = '';
	let isSubmitting = false;

	// Simple functions without reactive dependencies
	function clearMessages() {
		error = '';
		successMessage = '';
	}

	function setError(message: string) {
		error = message;
		successMessage = '';
	}

	function setSuccess(message: string) {
		successMessage = message;
		error = '';
	}

	// Load data function - simple async without reactive triggers
	async function loadData() {
		isLoading = true;
		clearMessages();

		try {
			// Load simulated users
			const usersResponse = await fetch('/api/admin/users?type=simulated&status=active&limit=50');
			if (usersResponse.ok) {
				const usersResult = await usersResponse.json();
				if (usersResult.success) {
					simulatedUsers = usersResult.data.users || [];
				}
			}

			// Load news items
			const newsResponse = await fetch('/api/admin/content/news?published=true&limit=20');
			if (newsResponse.ok) {
				const newsResult = await newsResponse.json();
				if (newsResult.success) {
					newsItems = newsResult.data || [];
				}
			}

			// Load gallery items
			const galleryResponse = await fetch('/api/admin/content/gallery?published=true&limit=20');
			if (galleryResponse.ok) {
				const galleryResult = await galleryResponse.json();
				if (galleryResult.success) {
					galleryItems = galleryResult.data || [];
				}
			}

		} catch (err) {
			console.error('Error loading data:', err);
			setError('Failed to load data. Please refresh the page.');
		} finally {
			isLoading = false;
		}
	}

	// Simple user selection toggle
	function toggleUser(userId: number) {
		if (selectedUserIds.includes(userId)) {
			selectedUserIds = selectedUserIds.filter(id => id !== userId);
		} else {
			selectedUserIds = [...selectedUserIds, userId];
		}
	}

	// Simple content type change handler
	function handleContentTypeChange() {
		selectedContentId = null; // Reset selection when type changes
	}

	// Get current content items based on selected type
	function getCurrentContentItems() {
		return selectedContentType === 'news' ? newsItems : galleryItems;
	}

	// Get selected content item
	function getSelectedContentItem() {
		const items = getCurrentContentItems();
		return items.find(item => item.id === selectedContentId) || null;
	}

	// Form validation
	function validateForm(): string | null {
		if (selectedUserIds.length === 0) {
			return 'Please select at least one user';
		}
		if (!selectedContentId) {
			return 'Please select content to comment on';
		}
		if (!customMessage.trim()) {
			return 'Please enter a message';
		}
		if (customMessage.trim().length < 5) {
			return 'Message must be at least 5 characters long';
		}
		return null;
	}

	// Submit form
	async function submitForm() {
		const validationError = validateForm();
		if (validationError) {
			setError(validationError);
			return;
		}

		isSubmitting = true;
		clearMessages();

		try {
			const response = await fetch('/api/admin/simulate-interactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					userIds: selectedUserIds,
					contentType: selectedContentType,
					contentId: selectedContentId,
					message: customMessage.trim()
				})
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccess(`Successfully created ${result.data.commentsCreated} comments`);
				// Reset form
				selectedUserIds = [];
				selectedContentId = null;
				customMessage = '';
			} else {
				setError(result.error || 'Failed to create comments');
			}
		} catch (err) {
			console.error('Error submitting form:', err);
			setError('An error occurred while creating comments');
		} finally {
			isSubmitting = false;
		}
	}

	// Initialize on mount
	onMount(() => {
		loadData();
	});
</script>

<div class="simulate-interactions">
	<header class="page-header">
		<div class="header-content">
			<h1>Simulate User Interactions</h1>
			<p class="header-subtitle">
				Generate simple user comments on published content using simulated accounts
			</p>
		</div>
		<div class="header-actions">
			<button
				class="btn secondary"
				onclick={() => goto('/admin')}
				disabled={isLoading || isSubmitting}
			>
				← Back to Admin
			</button>
		</div>
	</header>

	<!-- Messages -->
	{#if error}
		<ErrorMessage message={error} onDismiss={clearMessages} />
	{/if}

	{#if successMessage}
		<div class="message success" role="alert">
			✅ {successMessage}
		</div>
	{/if}

	{#if isLoading}
		<div class="loading-container">
			<LoadingSpinner />
			<p>Loading data...</p>
		</div>
	{:else}
		<form class="simulation-form" onsubmit={(e) => { e.preventDefault(); submitForm(); }}>
			<!-- User Selection -->
			<div class="form-section">
				<h3>Select Users</h3>
				<p class="section-description">Choose simulated users to post comments as:</p>
				
				{#if simulatedUsers.length === 0}
					<p class="no-data">No simulated users found. Please create some simulated users first.</p>
				{:else}
					<div class="users-grid">
						{#each simulatedUsers as user}
							<label class="user-card" class:selected={selectedUserIds.includes(user.id)}>
								<input
									type="checkbox"
									checked={selectedUserIds.includes(user.id)}
									onchange={() => toggleUser(user.id)}
									disabled={isSubmitting}
								/>
								<div class="user-info">
									<h4>{user.displayName}</h4>
									<p>@{user.username}</p>
									{#if user.bio}
										<p class="user-bio">{user.bio}</p>
									{/if}
								</div>
							</label>
						{/each}
					</div>
					<p class="selection-count">{selectedUserIds.length} users selected</p>
				{/if}
			</div>

			<!-- Content Selection -->
			<div class="form-section">
				<h3>Select Content</h3>
				<p class="section-description">Choose what content to comment on:</p>
				
				<div class="form-row">
					<div class="form-group">
						<label for="content-type">Content Type:</label>
						<select
							id="content-type"
							bind:value={selectedContentType}
							onchange={handleContentTypeChange}
							disabled={isSubmitting}
						>
							<option value="news">News Articles</option>
							<option value="gallery">Gallery Items</option>
						</select>
					</div>
				</div>

				<div class="form-group">
					<label for="content-item">Select Item:</label>
					<select
						id="content-item"
						bind:value={selectedContentId}
						disabled={isSubmitting}
					>
						<option value={null}>Choose an item...</option>
						{#each getCurrentContentItems() as item}
							<option value={item.id}>{item.title}</option>
						{/each}
					</select>
				</div>

				{#if getSelectedContentItem()}
					{@const selectedItem = getSelectedContentItem()}
					<div class="content-preview">
						<h4>Selected: {selectedItem.title}</h4>
						<p class="content-excerpt">
							{selectedItem.content?.substring(0, 150) || selectedItem.description?.substring(0, 150) || 'No description available'}...
						</p>
					</div>
				{/if}
			</div>

			<!-- Message Input -->
			<div class="form-section">
				<h3>Comment Message</h3>
				<p class="section-description">Enter the message to post as the selected users:</p>
				
				<div class="form-group">
					<label for="message">Message:</label>
					<textarea
						id="message"
						bind:value={customMessage}
						placeholder="Enter a comment message..."
						rows="4"
						disabled={isSubmitting}
						required
					></textarea>
					<small class="form-help">
						This message will be posted as a comment from each selected user.
					</small>
				</div>
			</div>

			<!-- Submit -->
			<div class="form-section">
				<button
					type="submit"
					class="btn primary large"
					disabled={isSubmitting || selectedUserIds.length === 0 || !selectedContentId || !customMessage.trim()}
				>
					{#if isSubmitting}
						<LoadingSpinner size="small" />
						Creating Comments...
					{:else}
						Create Comments ({selectedUserIds.length} users)
					{/if}
				</button>
			</div>
		</form>
	{/if}
</div>

<style>
	.simulate-interactions {
		padding: 2rem;
		max-width: 1200px;
		margin: 0 auto;
	}

	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		gap: 2rem;
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
	}

	.header-subtitle {
		color: var(--theme-text-secondary);
		margin: 0;
	}

	.simulation-form {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.form-section {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border-primary);
		border-radius: 8px;
		padding: 1.5rem;
	}

	.form-section h3 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
	}

	.section-description {
		color: var(--theme-text-secondary);
		margin: 0 0 1rem 0;
		font-size: 0.9rem;
	}

	.users-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.user-card {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
		padding: 1rem;
		border: 2px solid var(--theme-border-primary);
		border-radius: 6px;
		cursor: pointer;
		transition: all 0.2s ease;
		background: var(--theme-bg-primary);
	}

	.user-card:hover {
		border-color: var(--theme-accent-primary);
	}

	.user-card.selected {
		border-color: var(--theme-accent-primary);
		background: var(--theme-accent-primary-alpha);
	}

	.user-card input[type="checkbox"] {
		margin: 0;
	}

	.user-info h4 {
		margin: 0 0 0.25rem 0;
		color: var(--theme-text-primary);
	}

	.user-info p {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.user-bio {
		margin-top: 0.5rem !important;
		font-style: italic;
	}

	.selection-count {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		margin: 0;
	}

	.form-row {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.form-group label {
		font-weight: 500;
		color: var(--theme-text-primary);
	}

	.form-group select,
	.form-group textarea {
		padding: 0.75rem;
		border: 1px solid var(--theme-border-primary);
		border-radius: 4px;
		background: var(--theme-bg-primary);
		color: var(--theme-text-primary);
		font-family: inherit;
	}

	.form-group select:focus,
	.form-group textarea:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
	}

	.form-help {
		color: var(--theme-text-secondary);
		font-size: 0.85rem;
	}

	.content-preview {
		margin-top: 1rem;
		padding: 1rem;
		background: var(--theme-bg-tertiary);
		border-radius: 4px;
		border-left: 4px solid var(--theme-accent-primary);
	}

	.content-preview h4 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
	}

	.content-excerpt {
		color: var(--theme-text-secondary);
		margin: 0;
		font-size: 0.9rem;
	}

	.no-data {
		color: var(--theme-text-secondary);
		font-style: italic;
		text-align: center;
		padding: 2rem;
	}

	.message.success {
		background: var(--theme-success-bg);
		color: var(--theme-success-text);
		border: 1px solid var(--theme-success-border);
		padding: 1rem;
		border-radius: 4px;
		margin-bottom: 1rem;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
		padding: 3rem;
		color: var(--theme-text-secondary);
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 4px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		text-decoration: none;
	}

	.btn.primary {
		background: var(--theme-accent-primary);
		color: var(--theme-accent-text);
	}

	.btn.primary:hover:not(:disabled) {
		background: var(--theme-accent-primary-hover);
	}

	.btn.secondary {
		background: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border-primary);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	.btn.large {
		padding: 1rem 2rem;
		font-size: 1.1rem;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	/* Responsive */
	@media (max-width: 768px) {
		.simulate-interactions {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
		}

		.users-grid {
			grid-template-columns: 1fr;
		}

		.form-row {
			grid-template-columns: 1fr;
		}
	}
</style>
