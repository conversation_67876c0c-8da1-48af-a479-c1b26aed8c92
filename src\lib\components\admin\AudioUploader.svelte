<script>
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	// Props
	export let disabled = false;
	export let accept = 'audio/*';
	export let maxSize = 50 * 1024 * 1024; // 50MB default

	// State
	let fileInput;
	let isDragging = false;
	let uploading = false;
	let uploadProgress = 0;
	let error = null;

	// Supported audio formats
	const supportedFormats = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];

	/**
	 * Handle file selection
	 */
	function handleFileSelect(event) {
		const files = event.target.files || event.dataTransfer?.files;
		if (files && files.length > 0) {
			uploadFile(files[0]);
		}
	}

	/**
	 * Handle drag and drop
	 */
	function handleDrop(event) {
		event.preventDefault();
		isDragging = false;
		
		const files = event.dataTransfer.files;
		if (files && files.length > 0) {
			uploadFile(files[0]);
		}
	}

	function handleDragOver(event) {
		event.preventDefault();
		isDragging = true;
	}

	function handleDragLeave(event) {
		event.preventDefault();
		isDragging = false;
	}

	/**
	 * Validate audio file
	 */
	function validateFile(file) {
		// Check file type
		if (!supportedFormats.includes(file.type)) {
			return 'Unsupported audio format. Please use MP3, WAV, OGG, or M4A files.';
		}

		// Check file size
		if (file.size > maxSize) {
			const maxSizeMB = Math.round(maxSize / (1024 * 1024));
			return `File size too large. Maximum size is ${maxSizeMB}MB.`;
		}

		return null;
	}

	/**
	 * Upload audio file
	 */
	async function uploadFile(file) {
		error = null;
		
		// Validate file
		const validationError = validateFile(file);
		if (validationError) {
			error = validationError;
			return;
		}

		uploading = true;
		uploadProgress = 0;

		try {
			const formData = new FormData();
			formData.append('file', file);
			formData.append('type', 'audio');

			const response = await fetch('/api/upload', {
				method: 'POST',
				body: formData
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Upload failed');
			}

			const result = await response.json();
			
			// Dispatch success event with audio data
			dispatch('upload', {
				audioUrl: result.data.audioUrl,
				filename: result.data.filename,
				originalName: result.data.originalName,
				size: result.data.size,
				type: result.data.type,
				duration: result.data.duration
			});

			// Reset file input
			if (fileInput) {
				fileInput.value = '';
			}

		} catch (err) {
			console.error('Upload error:', err);
			error = err.message || 'Failed to upload audio file';
		} finally {
			uploading = false;
			uploadProgress = 0;
		}
	}

	/**
	 * Format file size
	 */
	function formatFileSize(bytes) {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div class="audio-uploader">
	<div 
		class="upload-area"
		class:dragging={isDragging}
		class:disabled
		on:drop={handleDrop}
		on:dragover={handleDragOver}
		on:dragleave={handleDragLeave}
		role="button"
		tabindex="0"
		aria-label="Audio file upload area"
	>
		{#if uploading}
			<div class="upload-progress">
				<div class="progress-spinner">⟳</div>
				<p>Uploading audio file...</p>
				{#if uploadProgress > 0}
					<div class="progress-bar">
						<div class="progress-fill" style="width: {uploadProgress}%"></div>
					</div>
					<span class="progress-text">{uploadProgress}%</span>
				{/if}
			</div>
		{:else}
			<div class="upload-content">
				<div class="upload-icon" aria-hidden="true">🎵</div>
				<h3>Upload Audio File</h3>
				<p>Drag and drop an audio file here, or click to browse</p>
				<p class="format-info">
					Supported formats: MP3, WAV, OGG, M4A<br>
					Maximum size: {formatFileSize(maxSize)}
				</p>
				
				<input
					bind:this={fileInput}
					type="file"
					{accept}
					{disabled}
					on:change={handleFileSelect}
					class="file-input"
					aria-label="Choose audio file"
				/>
				
				<button 
					class="browse-button"
					{disabled}
					on:click={() => fileInput?.click()}
					type="button"
				>
					Browse Files
				</button>
			</div>
		{/if}
	</div>

	{#if error}
		<div class="error-message" role="alert">
			<span class="error-icon" aria-hidden="true">⚠</span>
			{error}
		</div>
	{/if}
</div>

<style>
	.audio-uploader {
		width: 100%;
	}

	.upload-area {
		border: 2px dashed var(--color-border-secondary);
		border-radius: 8px;
		padding: 2rem;
		text-align: center;
		background-color: var(--color-surface-secondary);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
		cursor: pointer;
		position: relative;
	}

	.upload-area:hover:not(.disabled) {
		border-color: var(--color-interactive-primary);
		background-color: var(--color-surface-tertiary);
	}

	.upload-area.dragging {
		border-color: var(--color-interactive-primary);
		background-color: var(--color-interactive-bg);
	}

	.upload-area.disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.upload-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.upload-icon {
		font-size: 3rem;
		opacity: 0.7;
	}

	.upload-content h3 {
		margin: 0;
		font-size: 1.25rem;
		color: var(--color-text-primary);
	}

	.upload-content p {
		margin: 0;
		color: var(--color-text-secondary);
		line-height: 1.5;
	}

	.format-info {
		font-size: 0.9rem;
		opacity: 0.8;
	}

	.file-input {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
		cursor: pointer;
	}

	.browse-button {
		padding: 0.75rem 1.5rem;
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
		border: none;
		border-radius: 4px;
		font-size: 1rem;
		cursor: pointer;
		transition: var(--transition-theme);
	}

	.browse-button:hover:not(:disabled) {
		background-color: var(--color-interactive-primary-hover);
	}

	.browse-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.upload-progress {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.progress-spinner {
		font-size: 2rem;
		animation: spin 1s linear infinite;
		color: var(--color-interactive-primary);
	}

	@keyframes spin {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.progress-bar {
		width: 200px;
		height: 8px;
		background-color: var(--color-surface-tertiary);
		border-radius: 4px;
		overflow: hidden;
		border: 1px solid var(--color-border-primary);
	}

	.progress-fill {
		height: 100%;
		background-color: var(--color-interactive-primary);
		transition: width 0.3s ease;
	}

	.progress-text {
		font-size: 0.9rem;
		color: var(--color-text-secondary);
		font-weight: 500;
	}

	.error-message {
		margin-top: 1rem;
		padding: 0.75rem;
		background-color: var(--color-error-bg);
		color: var(--color-error);
		border: 1px solid var(--color-error-border);
		border-radius: 4px;
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-size: 0.9rem;
	}

	.error-icon {
		font-size: 1.1rem;
		flex-shrink: 0;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.upload-area {
			padding: 1.5rem;
		}

		.upload-icon {
			font-size: 2.5rem;
		}

		.upload-content h3 {
			font-size: 1.1rem;
		}

		.progress-bar {
			width: 150px;
		}
	}
</style>
