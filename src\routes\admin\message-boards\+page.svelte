<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// State management
	let boards: any[] = [];
	let isLoading = true;
	let error = '';
	let successMessage = '';

	// Modal states
	let showCreateModal = false;
	let showEditModal = false;
	let showDeleteModal = false;
	let selectedBoard: any = null;

	// Form data for create/edit
	let formData = {
		name: '',
		description: '',
		slug: '',
		icon: '',
		color: '#3B82F6',
		position: 0,
		isActive: true
	};

	// Available icons for boards
	const availableIcons = [
		{ value: 'fa-comments', label: 'Comments', icon: '💬' },
		{ value: 'fa-music', label: 'Music', icon: '🎵' },
		{ value: 'fa-film', label: 'Film', icon: '🎬' },
		{ value: 'fa-palette', label: 'Art', icon: '🎨' },
		{ value: 'fa-newspaper', label: 'News', icon: '📰' },
		{ value: 'fa-heart', label: 'Heart', icon: '❤️' },
		{ value: 'fa-star', label: 'Star', icon: '⭐' },
		{ value: 'fa-users', label: 'Users', icon: '👥' },
		{ value: 'fa-gamepad', label: 'Gaming', icon: '🎮' },
		{ value: 'fa-book', label: 'Book', icon: '📚' }
	];

	// Available colors for boards
	const availableColors = [
		{ value: '#3B82F6', label: 'Blue' },
		{ value: '#10B981', label: 'Green' },
		{ value: '#F59E0B', label: 'Yellow' },
		{ value: '#EF4444', label: 'Red' },
		{ value: '#8B5CF6', label: 'Purple' },
		{ value: '#EC4899', label: 'Pink' },
		{ value: '#06B6D4', label: 'Cyan' },
		{ value: '#84CC16', label: 'Lime' },
		{ value: '#F97316', label: 'Orange' },
		{ value: '#6B7280', label: 'Gray' }
	];

	// Utility functions
	function clearMessages() {
		error = '';
		successMessage = '';
	}

	function setError(message: string) {
		error = message;
		successMessage = '';
	}

	function setSuccess(message: string) {
		successMessage = message;
		error = '';
	}

	function resetForm() {
		formData = {
			name: '',
			description: '',
			slug: '',
			icon: '',
			color: '#3B82F6',
			position: 0,
			isActive: true
		};
	}

	function generateSlug(name: string): string {
		return name
			.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();
	}

	// Load boards data
	async function loadBoards() {
		isLoading = true;
		clearMessages();

		try {
			const response = await fetch('/api/admin/message-boards/boards');
			const result = await response.json();

			if (response.ok && result.success) {
				boards = result.data || [];
			} else {
				setError(result.error || 'Failed to load boards');
			}
		} catch (err) {
			console.error('Error loading boards:', err);
			setError('Failed to load boards');
		} finally {
			isLoading = false;
		}
	}

	// Create new board
	async function createBoard() {
		clearMessages();

		// Validate form
		if (!formData.name.trim()) {
			setError('Board name is required');
			return;
		}

		// Generate slug if not provided
		if (!formData.slug.trim()) {
			formData.slug = generateSlug(formData.name);
		}

		try {
			const response = await fetch('/api/admin/message-boards/boards', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccess('Board created successfully');
				showCreateModal = false;
				resetForm();
				await loadBoards();
			} else {
				setError(result.error || 'Failed to create board');
			}
		} catch (err) {
			console.error('Error creating board:', err);
			setError('Failed to create board');
		}
	}

	// Edit board
	async function editBoard() {
		clearMessages();

		if (!selectedBoard) return;

		try {
			const response = await fetch(`/api/admin/message-boards/boards/${selectedBoard.id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccess('Board updated successfully');
				showEditModal = false;
				selectedBoard = null;
				resetForm();
				await loadBoards();
			} else {
				setError(result.error || 'Failed to update board');
			}
		} catch (err) {
			console.error('Error updating board:', err);
			setError('Failed to update board');
		}
	}

	// Delete board
	async function deleteBoard() {
		clearMessages();

		if (!selectedBoard) return;

		try {
			const response = await fetch(`/api/admin/message-boards/boards/${selectedBoard.id}`, {
				method: 'DELETE'
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccess('Board deleted successfully');
				showDeleteModal = false;
				selectedBoard = null;
				await loadBoards();
			} else {
				setError(result.error || 'Failed to delete board');
			}
		} catch (err) {
			console.error('Error deleting board:', err);
			setError('Failed to delete board');
		}
	}

	// Modal handlers
	function openCreateModal() {
		resetForm();
		formData.position = boards.length + 1;
		showCreateModal = true;
	}

	function openEditModal(board: any) {
		selectedBoard = board;
		formData = {
			name: board.name,
			description: board.description || '',
			slug: board.slug,
			icon: board.icon || '',
			color: board.color || '#3B82F6',
			position: board.position,
			isActive: board.isActive
		};
		showEditModal = true;
	}

	function openDeleteModal(board: any) {
		selectedBoard = board;
		showDeleteModal = true;
	}

	function closeModals() {
		showCreateModal = false;
		showEditModal = false;
		showDeleteModal = false;
		selectedBoard = null;
		resetForm();
	}

	// Auto-generate slug when name changes
	function handleNameChange() {
		if (!formData.slug || formData.slug === generateSlug(formData.name)) {
			formData.slug = generateSlug(formData.name);
		}
	}

	// Initialize
	onMount(() => {
		loadBoards();
	});
</script>

<div class="message-boards-admin">
	<header class="page-header">
		<div class="header-content">
			<h1>Message Boards</h1>
			<p class="header-subtitle">
				Manage discussion boards, categories, and community organization
			</p>
		</div>
		<div class="header-actions">
			<button
				class="btn primary"
				onclick={openCreateModal}
				disabled={isLoading}
			>
				<i class="fa fa-plus" aria-hidden="true"></i>
				Create Board
			</button>
			<button
				class="btn secondary"
				onclick={() => goto('/admin')}
				disabled={isLoading}
			>
				← Back to Admin
			</button>
		</div>
	</header>

	<!-- Messages -->
	{#if error}
		<ErrorMessage message={error} onDismiss={clearMessages} />
	{/if}

	{#if successMessage}
		<div class="message success" role="alert">
			✅ {successMessage}
		</div>
	{/if}

	<!-- Loading State -->
	{#if isLoading}
		<div class="loading-container">
			<LoadingSpinner />
			<p>Loading message boards...</p>
		</div>
	{:else}
		<!-- Boards List -->
		<div class="boards-container">
			{#if boards.length === 0}
				<div class="empty-state">
					<i class="fa fa-comments fa-3x" aria-hidden="true"></i>
					<h3>No Message Boards</h3>
					<p>Create your first message board to get started with community discussions.</p>
					<button class="btn primary" onclick={openCreateModal}>
						Create First Board
					</button>
				</div>
			{:else}
				<div class="boards-grid">
					{#each boards as board}
						<div class="board-card" style="border-left-color: {board.color}">
							<div class="board-header">
								<div class="board-icon" style="color: {board.color}">
									{#if board.icon}
										<i class="fa {board.icon}" aria-hidden="true"></i>
									{:else}
										<i class="fa fa-comments" aria-hidden="true"></i>
									{/if}
								</div>
								<div class="board-info">
									<h3>{board.name}</h3>
									<p class="board-slug">/{board.slug}</p>
									{#if board.description}
										<p class="board-description">{board.description}</p>
									{/if}
								</div>
								<div class="board-status">
									<span class="status-badge" class:active={board.isActive} class:inactive={!board.isActive}>
										{board.isActive ? 'Active' : 'Inactive'}
									</span>
								</div>
							</div>
							
							<div class="board-stats">
								<div class="stat">
									<span class="stat-label">Position:</span>
									<span class="stat-value">{board.position}</span>
								</div>
								<div class="stat">
									<span class="stat-label">Topics:</span>
									<span class="stat-value">{board.topicCount || 0}</span>
								</div>
								<div class="stat">
									<span class="stat-label">Posts:</span>
									<span class="stat-value">{board.postCount || 0}</span>
								</div>
							</div>

							<div class="board-actions">
								<button
									class="btn small secondary"
									onclick={() => goto(`/admin/message-boards/boards/${board.id}/topics`)}
									title="Manage Topics"
								>
									<i class="fa fa-list" aria-hidden="true"></i>
									Topics
								</button>
								<button
									class="btn small secondary"
									onclick={() => openEditModal(board)}
									title="Edit Board"
								>
									<i class="fa fa-edit" aria-hidden="true"></i>
									Edit
								</button>
								<button
									class="btn small danger"
									onclick={() => openDeleteModal(board)}
									title="Delete Board"
								>
									<i class="fa fa-trash" aria-hidden="true"></i>
									Delete
								</button>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	{/if}
</div>

<!-- Create Board Modal -->
{#if showCreateModal}
	<div class="modal-overlay" tabindex="-1" role="dialog" aria-modal="true" aria-labelledby="create-modal-title">
		<div class="modal">
			<div class="modal-header">
				<h2 id="create-modal-title">Create New Board</h2>
				<button class="modal-close" onclick={closeModals} aria-label="Close modal">
					<i class="fa fa-times" aria-hidden="true"></i>
				</button>
			</div>

			<form class="modal-body" onsubmit={(e) => { e.preventDefault(); createBoard(); }}>
				<div class="form-group">
					<label for="create-name">Board Name *</label>
					<input
						id="create-name"
						type="text"
						bind:value={formData.name}
						oninput={handleNameChange}
						placeholder="Enter board name"
						required
					/>
				</div>

				<div class="form-group">
					<label for="create-slug">URL Slug *</label>
					<input
						id="create-slug"
						type="text"
						bind:value={formData.slug}
						placeholder="auto-generated-from-name"
						required
					/>
					<small class="form-help">Used in URLs: /boards/{formData.slug}</small>
				</div>

				<div class="form-group">
					<label for="create-description">Description</label>
					<textarea
						id="create-description"
						bind:value={formData.description}
						placeholder="Brief description of this board's purpose"
						rows="3"
					></textarea>
				</div>

				<div class="form-row">
					<div class="form-group">
						<label for="create-icon">Icon</label>
						<select id="create-icon" bind:value={formData.icon}>
							<option value="">No Icon</option>
							{#each availableIcons as iconOption}
								<option value={iconOption.value}>{iconOption.icon} {iconOption.label}</option>
							{/each}
						</select>
					</div>

					<div class="form-group">
						<label for="create-color">Color</label>
						<select id="create-color" bind:value={formData.color}>
							{#each availableColors as colorOption}
								<option value={colorOption.value}>{colorOption.label}</option>
							{/each}
						</select>
						<div class="color-preview" style="background-color: {formData.color}"></div>
					</div>
				</div>

				<div class="form-row">
					<div class="form-group">
						<label for="create-position">Position</label>
						<input
							id="create-position"
							type="number"
							bind:value={formData.position}
							min="1"
							placeholder="Display order"
						/>
					</div>

					<div class="form-group">
						<label class="checkbox-label">
							<input
								type="checkbox"
								bind:checked={formData.isActive}
							/>
							Active Board
						</label>
					</div>
				</div>
			</form>

			<div class="modal-footer">
				<button class="btn secondary" onclick={closeModals}>Cancel</button>
				<button class="btn primary" onclick={createBoard}>Create Board</button>
			</div>
		</div>
	</div>
{/if}

<!-- Edit Board Modal -->
{#if showEditModal}
	<div class="modal-overlay" tabindex="-1" role="dialog" aria-modal="true" aria-labelledby="edit-modal-title">
		<div class="modal">
			<div class="modal-header">
				<h2 id="edit-modal-title">Edit Board</h2>
				<button class="modal-close" onclick={closeModals} aria-label="Close modal">
					<i class="fa fa-times" aria-hidden="true"></i>
				</button>
			</div>

			<form class="modal-body" onsubmit={(e) => { e.preventDefault(); editBoard(); }}>
				<div class="form-group">
					<label for="edit-name">Board Name *</label>
					<input
						id="edit-name"
						type="text"
						bind:value={formData.name}
						placeholder="Enter board name"
						required
					/>
				</div>

				<div class="form-group">
					<label for="edit-slug">URL Slug *</label>
					<input
						id="edit-slug"
						type="text"
						bind:value={formData.slug}
						placeholder="board-url-slug"
						required
					/>
					<small class="form-help">Used in URLs: /boards/{formData.slug}</small>
				</div>

				<div class="form-group">
					<label for="edit-description">Description</label>
					<textarea
						id="edit-description"
						bind:value={formData.description}
						placeholder="Brief description of this board's purpose"
						rows="3"
					></textarea>
				</div>

				<div class="form-row">
					<div class="form-group">
						<label for="edit-icon">Icon</label>
						<select id="edit-icon" bind:value={formData.icon}>
							<option value="">No Icon</option>
							{#each availableIcons as iconOption}
								<option value={iconOption.value}>{iconOption.icon} {iconOption.label}</option>
							{/each}
						</select>
					</div>

					<div class="form-group">
						<label for="edit-color">Color</label>
						<select id="edit-color" bind:value={formData.color}>
							{#each availableColors as colorOption}
								<option value={colorOption.value}>{colorOption.label}</option>
							{/each}
						</select>
						<div class="color-preview" style="background-color: {formData.color}"></div>
					</div>
				</div>

				<div class="form-row">
					<div class="form-group">
						<label for="edit-position">Position</label>
						<input
							id="edit-position"
							type="number"
							bind:value={formData.position}
							min="1"
							placeholder="Display order"
						/>
					</div>

					<div class="form-group">
						<label class="checkbox-label">
							<input
								type="checkbox"
								bind:checked={formData.isActive}
							/>
							Active Board
						</label>
					</div>
				</div>
			</form>

			<div class="modal-footer">
				<button class="btn secondary" onclick={closeModals}>Cancel</button>
				<button class="btn primary" onclick={editBoard}>Update Board</button>
			</div>
		</div>
	</div>
{/if}

<!-- Delete Board Modal -->
{#if showDeleteModal && selectedBoard}
	<div class="modal-overlay" tabindex="-1" role="dialog" aria-modal="true" aria-labelledby="delete-modal-title">
		<div class="modal">
			<div class="modal-header">
				<h2 id="delete-modal-title">Delete Board</h2>
				<button class="modal-close" onclick={closeModals} aria-label="Close modal">
					<i class="fa fa-times" aria-hidden="true"></i>
				</button>
			</div>

			<div class="modal-body">
				<div class="warning-message">
					<i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
					<p>Are you sure you want to delete the board <strong>"{selectedBoard.name}"</strong>?</p>
					<p class="warning-text">This action cannot be undone. All topics and posts in this board will also be deleted.</p>
				</div>
			</div>

			<div class="modal-footer">
				<button class="btn secondary" onclick={closeModals}>Cancel</button>
				<button class="btn danger" onclick={deleteBoard}>Delete Board</button>
			</div>
		</div>
	</div>
{/if}

<style>
	.message-boards-admin {
		padding: 2rem;
		max-width: 1400px;
		margin: 0 auto;
	}

	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		gap: 2rem;
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 2rem;
		font-weight: 600;
	}

	.header-subtitle {
		color: var(--theme-text-secondary);
		margin: 0;
		font-size: 1rem;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		flex-shrink: 0;
	}

	.message.success {
		background: var(--theme-success-bg);
		color: var(--theme-success-text);
		border: 1px solid var(--theme-success-border);
		padding: 1rem;
		border-radius: 6px;
		margin-bottom: 1.5rem;
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
		padding: 4rem;
		color: var(--theme-text-secondary);
	}

	.boards-container {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border-primary);
		border-radius: 8px;
		overflow: hidden;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		color: var(--theme-text-secondary);
	}

	.empty-state i {
		color: var(--theme-text-tertiary);
		margin-bottom: 1rem;
	}

	.empty-state h3 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
	}

	.empty-state p {
		margin: 0 0 2rem 0;
		max-width: 400px;
		margin-left: auto;
		margin-right: auto;
	}

	.boards-grid {
		display: grid;
		gap: 1px;
		background: var(--theme-border-primary);
	}

	.board-card {
		background: var(--theme-bg-primary);
		padding: 1.5rem;
		border-left: 4px solid var(--theme-accent-primary);
		transition: all 0.2s ease;
	}

	.board-card:hover {
		background: var(--theme-bg-secondary);
	}

	.board-header {
		display: flex;
		align-items: flex-start;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.board-icon {
		font-size: 1.5rem;
		width: 2.5rem;
		height: 2.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		background: var(--theme-bg-secondary);
		border-radius: 6px;
		flex-shrink: 0;
	}

	.board-info {
		flex: 1;
		min-width: 0;
	}

	.board-info h3 {
		margin: 0 0 0.25rem 0;
		color: var(--theme-text-primary);
		font-size: 1.25rem;
		font-weight: 600;
	}

	.board-slug {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-tertiary);
		font-family: var(--theme-font-mono);
		font-size: 0.875rem;
	}

	.board-description {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		line-height: 1.4;
	}

	.board-status {
		flex-shrink: 0;
	}

	.status-badge {
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.75rem;
		font-weight: 500;
		text-transform: uppercase;
		letter-spacing: 0.05em;
	}

	.status-badge.active {
		background: var(--theme-success-bg);
		color: var(--theme-success-text);
		border: 1px solid var(--theme-success-border);
	}

	.status-badge.inactive {
		background: var(--theme-warning-bg);
		color: var(--theme-warning-text);
		border: 1px solid var(--theme-warning-border);
	}

	.board-stats {
		display: flex;
		gap: 2rem;
		margin-bottom: 1rem;
		padding: 0.75rem 0;
		border-top: 1px solid var(--theme-border-secondary);
		border-bottom: 1px solid var(--theme-border-secondary);
	}

	.stat {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
	}

	.stat-label {
		font-size: 0.75rem;
		color: var(--theme-text-tertiary);
		text-transform: uppercase;
		letter-spacing: 0.05em;
		font-weight: 500;
	}

	.stat-value {
		font-size: 1.25rem;
		font-weight: 600;
		color: var(--theme-text-primary);
	}

	.board-actions {
		display: flex;
		gap: 0.5rem;
		justify-content: flex-end;
	}

	/* Button Styles */
	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		text-decoration: none;
		font-size: 0.9rem;
		line-height: 1;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background: var(--theme-accent-primary);
		color: var(--theme-accent-text);
	}

	.btn.primary:hover:not(:disabled) {
		background: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border-primary);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	.btn.danger {
		background: var(--theme-error-bg);
		color: var(--theme-error-text);
		border: 1px solid var(--theme-error-border);
	}

	.btn.danger:hover:not(:disabled) {
		background: var(--theme-error-bg-hover);
		transform: translateY(-1px);
	}

	.btn.small {
		padding: 0.5rem 1rem;
		font-size: 0.8rem;
	}

	.btn i {
		font-size: 0.875em;
	}

	/* Modal Styles */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.modal {
		background: var(--theme-bg-primary);
		border: 1px solid var(--theme-border-primary);
		border-radius: 8px;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
		max-width: 600px;
		width: 100%;
		max-height: 90vh;
		overflow-y: auto;
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid var(--theme-border-secondary);
	}

	.modal-header h2 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
		font-weight: 600;
	}

	.modal-close {
		background: none;
		border: none;
		color: var(--theme-text-secondary);
		cursor: pointer;
		padding: 0.5rem;
		border-radius: 4px;
		transition: all 0.2s ease;
	}

	.modal-close:hover {
		background: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
	}

	.modal-body {
		padding: 1.5rem;
	}

	.modal-footer {
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
		padding: 1.5rem;
		border-top: 1px solid var(--theme-border-secondary);
		background: var(--theme-bg-secondary);
	}

	/* Form Styles */
	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 500;
		color: var(--theme-text-primary);
	}

	.form-group input,
	.form-group textarea,
	.form-group select {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid var(--theme-border-primary);
		border-radius: 4px;
		background: var(--theme-bg-primary);
		color: var(--theme-text-primary);
		font-family: inherit;
		font-size: 0.9rem;
		transition: border-color 0.2s ease;
	}

	.form-group input:focus,
	.form-group textarea:focus,
	.form-group select:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px var(--theme-accent-primary-alpha);
	}

	.form-group textarea {
		resize: vertical;
		min-height: 80px;
	}

	.form-help {
		display: block;
		margin-top: 0.25rem;
		font-size: 0.8rem;
		color: var(--theme-text-secondary);
	}

	.form-row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
	}

	.checkbox-label {
		display: flex !important;
		align-items: center;
		gap: 0.5rem;
		cursor: pointer;
		margin-bottom: 0 !important;
	}

	.checkbox-label input[type="checkbox"] {
		width: auto !important;
		margin: 0;
	}

	.color-preview {
		width: 2rem;
		height: 2rem;
		border-radius: 4px;
		border: 1px solid var(--theme-border-primary);
		margin-top: 0.5rem;
	}

	.warning-message {
		text-align: center;
		padding: 1rem;
	}

	.warning-message i {
		font-size: 2rem;
		color: var(--theme-warning-text);
		margin-bottom: 1rem;
	}

	.warning-message p {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
	}

	.warning-text {
		color: var(--theme-text-secondary) !important;
		font-size: 0.9rem;
	}

	/* Responsive */
	@media (max-width: 768px) {
		.message-boards-admin {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
		}

		.header-actions {
			justify-content: stretch;
		}

		.header-actions .btn {
			flex: 1;
			justify-content: center;
		}

		.board-header {
			flex-direction: column;
			gap: 0.75rem;
		}

		.board-stats {
			gap: 1rem;
		}

		.board-actions {
			justify-content: stretch;
		}

		.board-actions .btn {
			flex: 1;
			justify-content: center;
		}

		.form-row {
			grid-template-columns: 1fr;
		}

		.modal {
			margin: 0.5rem;
		}

		.modal-header,
		.modal-body,
		.modal-footer {
			padding: 1rem;
		}
	}
</style>
