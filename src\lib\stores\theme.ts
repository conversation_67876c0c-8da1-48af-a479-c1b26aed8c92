import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'light' | 'dark';
export type ThemePreference = 'light' | 'dark' | 'auto';

export interface ThemeState {
	current: Theme;
	preference: ThemePreference;
	systemTheme: Theme;
}

// Create the theme store
function createThemeStore() {
	// Default state
	const defaultState: ThemeState = {
		current: 'light',
		preference: 'auto',
		systemTheme: 'light'
	};

	// Initialize with default state
	const { subscribe, set, update } = writable<ThemeState>(defaultState);

	return {
		subscribe,

		/**
		 * Initialize theme from localStorage and system preference
		 */
		init: () => {
			if (browser) {
				const systemTheme = getSystemTheme();
				const storedPreference = localStorage.getItem('fwfc-theme-preference') as ThemePreference;
				const preference = storedPreference && ['light', 'dark', 'auto'].includes(storedPreference)
					? storedPreference
					: 'auto';

				const current = preference === 'auto' ? systemTheme : preference as Theme;

				const newState: ThemeState = {
					current,
					preference,
					systemTheme
				};

				set(newState);
				applyTheme(current);
			}
		},

		/**
		 * Set theme preference and persist to localStorage
		 */
		setPreference: (preference: ThemePreference) => {
			if (browser) {
				localStorage.setItem('fwfc-theme-preference', preference);

				update(state => {
					const current = preference === 'auto' ? state.systemTheme : preference as Theme;
					const newState = { ...state, preference, current };
					applyTheme(current);
					return newState;
				});
			}
		},

		/**
		 * Update system theme (called when system preference changes)
		 */
		updateSystemTheme: (systemTheme: Theme) => {
			update(state => {
				const current = state.preference === 'auto' ? systemTheme : state.current;
				const newState = { ...state, systemTheme, current };
				if (state.preference === 'auto') {
					applyTheme(current);
				}
				return newState;
			});
		},

		/**
		 * Toggle between light, dark, and auto themes
		 */
		toggle: () => {
			update(state => {
				let newPreference: ThemePreference;

				switch (state.preference) {
					case 'light':
						newPreference = 'dark';
						break;
					case 'dark':
						newPreference = 'auto';
						break;
					case 'auto':
						newPreference = 'light';
						break;
					default:
						newPreference = 'light';
				}

				const current = newPreference === 'auto' ? state.systemTheme : newPreference as Theme;

				if (browser) {
					localStorage.setItem('fwfc-theme-preference', newPreference);
					applyTheme(current);
				}

				return { ...state, preference: newPreference, current };
			});
		},

		/**
		 * Get current theme (for backward compatibility)
		 */
		getCurrentTheme: () => {
			let currentTheme: Theme = 'light';
			subscribe(state => {
				currentTheme = state.current;
			})();
			return currentTheme;
		}
	};
}

/**
 * Apply theme to document with smooth transitions
 */
function applyTheme(theme: Theme) {
	if (browser && document.documentElement) {
		// Add transition class temporarily to enable smooth theme switching
		document.documentElement.classList.add('theme-transitioning');

		// Always set the data-theme attribute to the current theme
		document.documentElement.setAttribute('data-theme', theme);

		// Remove transition class after animation completes
		setTimeout(() => {
			document.documentElement.classList.remove('theme-transitioning');
		}, 300);
	}
}

/**
 * Get system theme preference with fallback
 */
export function getSystemTheme(): Theme {
	if (browser && window.matchMedia) {
		try {
			return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
		} catch (error) {
			console.warn('Failed to detect system theme preference:', error);
			return 'light';
		}
	}
	return 'light';
}

/**
 * Listen for system theme changes with error handling
 */
export function watchSystemTheme(callback: (theme: Theme) => void): () => void {
	if (browser && window.matchMedia) {
		try {
			const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

			const handler = (e: MediaQueryListEvent) => {
				try {
					callback(e.matches ? 'dark' : 'light');
				} catch (error) {
					console.error('Error in system theme change handler:', error);
				}
			};

			mediaQuery.addEventListener('change', handler);

			// Return cleanup function
			return () => {
				try {
					mediaQuery.removeEventListener('change', handler);
				} catch (error) {
					console.warn('Error removing system theme listener:', error);
				}
			};
		} catch (error) {
			console.warn('Failed to set up system theme watcher:', error);
		}
	}

	return () => {}; // No-op cleanup for non-browser environments
}

// Export the theme store
export const theme = createThemeStore();
