import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import { boards, topics, posts } from '$lib/server/db/schema';
import { eq, sql, and, desc } from 'drizzle-orm';

// GET - List all active boards with statistics for public viewing
export const GET: RequestHandler = async () => {
	try {
		// Get active boards with topic and post counts, plus recent activity
		const boardsWithStats = await db
			.select({
				id: boards.id,
				name: boards.name,
				description: boards.description,
				slug: boards.slug,
				icon: boards.icon,
				color: boards.color,
				position: boards.position,
				createdAt: boards.createdAt,
				topicCount: sql<number>`(
					SELECT COUNT(*) FROM ${topics} 
					WHERE ${topics.boardId} = ${boards.id}
					AND ${topics.isArchived} = 0
				)`,
				postCount: sql<number>`(
					SELECT COUNT(*) FROM ${posts} 
					WHERE ${posts.topicId} IN (
						SELECT ${topics.id} FROM ${topics} 
						WHERE ${topics.boardId} = ${boards.id}
						AND ${topics.isArchived} = 0
					)
					AND ${posts.isDeleted} = 0
				)`,
				lastActivityAt: sql<string>`(
					SELECT MAX(${posts.createdAt}) FROM ${posts}
					WHERE ${posts.topicId} IN (
						SELECT ${topics.id} FROM ${topics}
						WHERE ${topics.boardId} = ${boards.id}
						AND ${topics.isArchived} = 0
					)
					AND ${posts.isDeleted} = 0
				)`,
				lastTopicTitle: sql<string>`(
					SELECT ${topics.title} FROM ${topics}
					WHERE ${topics.boardId} = ${boards.id}
					AND ${topics.isArchived} = 0
					ORDER BY ${topics.lastPostAt} DESC, ${topics.createdAt} DESC
					LIMIT 1
				)`,
				lastTopicSlug: sql<string>`(
					SELECT ${topics.slug} FROM ${topics}
					WHERE ${topics.boardId} = ${boards.id}
					AND ${topics.isArchived} = 0
					ORDER BY ${topics.lastPostAt} DESC, ${topics.createdAt} DESC
					LIMIT 1
				)`
			})
			.from(boards)
			.where(eq(boards.isActive, true))
			.orderBy(boards.position, boards.createdAt);

		// Format the response with better data structure
		const formattedBoards = boardsWithStats.map(board => ({
			id: board.id,
			name: board.name,
			description: board.description,
			slug: board.slug,
			icon: board.icon,
			color: board.color,
			position: board.position,
			createdAt: board.createdAt,
			stats: {
				topicCount: board.topicCount || 0,
				postCount: board.postCount || 0,
				lastActivityAt: board.lastActivityAt,
				lastTopic: board.lastTopicTitle ? {
					title: board.lastTopicTitle,
					slug: board.lastTopicSlug
				} : null
			}
		}));

		return json({
			success: true,
			data: formattedBoards
		});

	} catch (error) {
		console.error('Error fetching public boards:', error);
		return json({ 
			success: false, 
			error: 'Failed to load message boards' 
		}, { status: 500 });
	}
};
