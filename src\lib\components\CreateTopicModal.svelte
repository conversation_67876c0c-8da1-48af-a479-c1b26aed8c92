<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	export let isOpen = false;
	export let boardName = '';
	export let isLoading = false;

	const dispatch = createEventDispatcher();

	// Form state
	let title = '';
	let description = '';
	let content = '';
	let errors: Record<string, string> = {};

	// Utility functions
	function clearErrors() {
		errors = {};
	}

	function validateForm(): boolean {
		clearErrors();
		let isValid = true;

		if (!title.trim()) {
			errors.title = 'Title is required';
			isValid = false;
		} else if (title.trim().length < 3) {
			errors.title = 'Title must be at least 3 characters long';
			isValid = false;
		} else if (title.trim().length > 200) {
			errors.title = 'Title must be less than 200 characters';
			isValid = false;
		}

		if (!content.trim()) {
			errors.content = 'Content is required';
			isValid = false;
		} else if (content.trim().length < 10) {
			errors.content = 'Content must be at least 10 characters long';
			isValid = false;
		}

		if (description && description.trim().length > 500) {
			errors.description = 'Description must be less than 500 characters';
			isValid = false;
		}

		return isValid;
	}

	function handleSubmit(event: Event) {
		event.preventDefault();
		if (!validateForm()) return;

		const topicData = {
			title: title.trim(),
			description: description.trim() || null,
			content: content.trim()
		};

		dispatch('submit', topicData);
	}

	function handleCancel() {
		// Reset form
		title = '';
		description = '';
		content = '';
		clearErrors();
		dispatch('cancel');
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleCancel();
		}
	}

	// Auto-focus title input when modal opens
	let titleInput: HTMLInputElement;
	$: if (isOpen && titleInput) {
		setTimeout(() => titleInput.focus(), 100);
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<div
		class="modal-overlay"
		onclick={handleCancel}
		onkeydown={(e) => e.key === 'Escape' && handleCancel()}
		role="dialog"
		aria-modal="true"
		aria-labelledby="modal-title"
		tabindex="-1"
	>
		<div class="modal-content">
			<header class="modal-header">
				<h2 id="modal-title">Create New Topic</h2>
				<p class="modal-subtitle">Start a new discussion in <strong>{boardName}</strong></p>
				<button
					class="close-button"
					onclick={handleCancel}
					aria-label="Close modal"
					disabled={isLoading}
				>
					<i class="fa fa-times" aria-hidden="true"></i>
				</button>
			</header>

			<form class="modal-form" onsubmit={handleSubmit}>
				<!-- Title Field -->
				<div class="form-group">
					<label for="topic-title" class="form-label">
						Topic Title <span class="required">*</span>
					</label>
					<input
						id="topic-title"
						bind:this={titleInput}
						bind:value={title}
						type="text"
						class="form-input"
						class:error={errors.title}
						placeholder="Enter a descriptive title for your topic"
						maxlength="200"
						disabled={isLoading}
						aria-describedby={errors.title ? 'title-error' : undefined}
					/>
					{#if errors.title}
						<div id="title-error" class="error-message" role="alert">
							<i class="fa fa-exclamation-circle" aria-hidden="true"></i>
							{errors.title}
						</div>
					{/if}
					<div class="character-count">
						{title.length}/200 characters
					</div>
				</div>

				<!-- Description Field -->
				<div class="form-group">
					<label for="topic-description" class="form-label">
						Description <span class="optional">(optional)</span>
					</label>
					<input
						id="topic-description"
						bind:value={description}
						type="text"
						class="form-input"
						class:error={errors.description}
						placeholder="Brief description of what this topic is about"
						maxlength="500"
						disabled={isLoading}
						aria-describedby={errors.description ? 'description-error' : undefined}
					/>
					{#if errors.description}
						<div id="description-error" class="error-message" role="alert">
							<i class="fa fa-exclamation-circle" aria-hidden="true"></i>
							{errors.description}
						</div>
					{/if}
					<div class="character-count">
						{description.length}/500 characters
					</div>
				</div>

				<!-- Content Field -->
				<div class="form-group">
					<label for="topic-content" class="form-label">
						Content <span class="required">*</span>
					</label>
					<textarea
						id="topic-content"
						bind:value={content}
						class="form-textarea"
						class:error={errors.content}
						placeholder="Write your topic content here..."
						rows="8"
						disabled={isLoading}
						aria-describedby={errors.content ? 'content-error' : undefined}
					></textarea>
					{#if errors.content}
						<div id="content-error" class="error-message" role="alert">
							<i class="fa fa-exclamation-circle" aria-hidden="true"></i>
							{errors.content}
						</div>
					{/if}
					<div class="character-count">
						{content.length} characters
					</div>
				</div>

				<!-- Form Actions -->
				<div class="form-actions">
					<button
						type="button"
						class="btn secondary"
						onclick={handleCancel}
						disabled={isLoading}
					>
						Cancel
					</button>
					<button
						type="submit"
						class="btn primary"
						disabled={isLoading || !title.trim() || !content.trim()}
					>
						{#if isLoading}
							<i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
							Creating...
						{:else}
							<i class="fa fa-plus" aria-hidden="true"></i>
							Create Topic
						{/if}
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}

<style>
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 1rem;
	}

	.modal-content {
		background: var(--theme-bg-secondary);
		border: 1px solid var(--theme-border-primary);
		border-radius: 12px;
		width: 100%;
		max-width: 600px;
		max-height: 90vh;
		overflow-y: auto;
		box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
		position: relative;
	}

	.modal-header {
		padding: 2rem 2rem 1rem 2rem;
		border-bottom: 1px solid var(--theme-border-secondary);
		position: relative;
	}

	.modal-header h2 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
		font-weight: 600;
		line-height: 1.3;
	}

	.modal-subtitle {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		line-height: 1.4;
	}

	.close-button {
		position: absolute;
		top: 1.5rem;
		right: 1.5rem;
		background: none;
		border: none;
		color: var(--theme-text-secondary);
		font-size: 1.25rem;
		cursor: pointer;
		padding: 0.5rem;
		border-radius: 6px;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 2.5rem;
		height: 2.5rem;
	}

	.close-button:hover:not(:disabled) {
		background: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
	}

	.close-button:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.modal-form {
		padding: 1.5rem 2rem 2rem 2rem;
	}

	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-label {
		display: block;
		margin-bottom: 0.5rem;
		color: var(--theme-text-primary);
		font-weight: 500;
		font-size: 0.9rem;
	}

	.required {
		color: var(--theme-error-text);
	}

	.optional {
		color: var(--theme-text-tertiary);
		font-weight: 400;
	}

	.form-input,
	.form-textarea {
		width: 100%;
		padding: 0.75rem 1rem;
		border: 1px solid var(--theme-border-primary);
		border-radius: 6px;
		background: var(--theme-bg-primary);
		color: var(--theme-text-primary);
		font-size: 0.9rem;
		line-height: 1.5;
		transition: all 0.2s ease;
		font-family: inherit;
	}

	.form-input:focus,
	.form-textarea:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
	}

	.form-input.error,
	.form-textarea.error {
		border-color: var(--theme-error-border);
		box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
	}

	.form-input:disabled,
	.form-textarea:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		background: var(--theme-bg-tertiary);
	}

	.form-textarea {
		resize: vertical;
		min-height: 120px;
	}

	.character-count {
		margin-top: 0.25rem;
		font-size: 0.75rem;
		color: var(--theme-text-tertiary);
		text-align: right;
	}

	.error-message {
		margin-top: 0.5rem;
		color: var(--theme-error-text);
		font-size: 0.8rem;
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		justify-content: flex-end;
		margin-top: 2rem;
		padding-top: 1.5rem;
		border-top: 1px solid var(--theme-border-secondary);
	}

	.btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 6px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		text-decoration: none;
		font-size: 0.9rem;
		line-height: 1;
		min-width: 120px;
		justify-content: center;
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.btn.primary {
		background: var(--theme-accent-primary);
		color: white;
	}

	.btn.primary:hover:not(:disabled) {
		background: var(--theme-accent-secondary);
		transform: translateY(-1px);
	}

	.btn.secondary {
		background: var(--theme-bg-secondary);
		color: var(--theme-text-primary);
		border: 1px solid var(--theme-border-primary);
	}

	.btn.secondary:hover:not(:disabled) {
		background: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.modal-overlay {
			padding: 0.5rem;
		}

		.modal-content {
			max-height: 95vh;
		}

		.modal-header {
			padding: 1.5rem 1.5rem 1rem 1.5rem;
		}

		.modal-header h2 {
			font-size: 1.25rem;
		}

		.close-button {
			top: 1rem;
			right: 1rem;
		}

		.modal-form {
			padding: 1rem 1.5rem 1.5rem 1.5rem;
		}

		.form-actions {
			flex-direction: column-reverse;
		}

		.btn {
			width: 100%;
		}
	}

	/* High Contrast Mode Support */
	@media (prefers-contrast: high) {
		.modal-content {
			border-width: 2px;
		}

		.form-input,
		.form-textarea {
			border-width: 2px;
		}

		.btn {
			border-width: 2px;
		}
	}

	/* Reduced Motion Support */
	@media (prefers-reduced-motion: reduce) {
		.btn {
			transition: none;
		}

		.btn.primary:hover:not(:disabled) {
			transform: none;
		}
	}
</style>
