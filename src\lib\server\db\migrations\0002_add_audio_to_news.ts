import { sql } from 'drizzle-orm';

export async function up(db: any) {
  // Add audio fields to news table
  await db.run(sql`ALTER TABLE news ADD COLUMN audio_url TEXT`);
  await db.run(sql`ALTER TABLE news ADD COLUMN audio_title TEXT`);
  await db.run(sql`ALTER TABLE news ADD COLUMN audio_duration INTEGER`);
  await db.run(sql`ALTER TABLE news ADD COLUMN audio_type TEXT`);
  await db.run(sql`ALTER TABLE news ADD COLUMN audio_size INTEGER`);
}

export async function down(db: any) {
  // Note: SQLite doesn't support dropping columns directly
  // This would require recreating the table without the audio columns
  console.log('Rollback not implemented for SQLite column drops');
}
