import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Connect to the database
const db = new Database('local.db');

console.log('Adding audio fields to news table...');

try {
  // Check if audio_url column already exists
  const tableInfo = db.prepare("PRAGMA table_info(news)").all();
  const hasAudioUrl = tableInfo.some(column => column.name === 'audio_url');
  
  if (hasAudioUrl) {
    console.log('Audio fields already exist in news table.');
  } else {
    // Add audio fields to news table
    db.exec('ALTER TABLE news ADD COLUMN audio_url TEXT');
    db.exec('ALTER TABLE news ADD COLUMN audio_title TEXT');
    db.exec('ALTER TABLE news ADD COLUMN audio_duration INTEGER');
    db.exec('ALTER TABLE news ADD COLUMN audio_type TEXT');
    db.exec('ALTER TABLE news ADD COLUMN audio_size INTEGER');
    
    console.log('Successfully added audio fields to news table!');
  }
} catch (error) {
  console.error('Error adding audio fields:', error);
} finally {
  db.close();
}
