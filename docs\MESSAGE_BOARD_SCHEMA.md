# Message Board Database Schema Design

## Overview
This document outlines the comprehensive database schema design for the FWFC message board system, integrating with the existing user management, audit logging, and content authorship systems.

## New Tables

### 1. Boards (Message Board Categories)
```sql
CREATE TABLE boards (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  slug TEXT UNIQUE NOT NULL,
  icon TEXT, -- Font Awesome icon class (e.g., 'fa-music', 'fa-film')
  color TEXT, -- Hex color for theming (e.g., '#3B82F6')
  position INTEGER DEFAULT 0, -- For custom ordering
  is_active INTEGER DEFAULT 1, -- Boolean: 1=active, 0=inactive
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Purpose**: Categories for organizing discussions (e.g., "General Discussion", "Music", "Acting Projects", "Fan Art")

### 2. Topics (Discussion Threads)
```sql
CREATE TABLE topics (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  board_id INTEGER NOT NULL REFERENCES boards(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  author_id INTEGER NOT NULL REFERENCES users(id),
  is_pinned INTEGER DEFAULT 0, -- Boolean: pinned topics appear first
  is_locked INTEGER DEFAULT 0, -- Boolean: locked topics prevent new posts
  is_archived INTEGER DEFAULT 0, -- Boolean: archived topics are read-only
  view_count INTEGER DEFAULT 0,
  post_count INTEGER DEFAULT 0,
  last_post_id INTEGER REFERENCES posts(id),
  last_post_at TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(board_id, slug)
);
```

**Purpose**: Individual discussion threads within boards

### 3. Posts (Messages within Topics)
```sql
CREATE TABLE posts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  topic_id INTEGER NOT NULL REFERENCES topics(id) ON DELETE CASCADE,
  parent_post_id INTEGER REFERENCES posts(id), -- For threading/replies
  author_id INTEGER NOT NULL REFERENCES users(id),
  content TEXT NOT NULL,
  is_first_post INTEGER DEFAULT 0, -- Boolean: marks the original topic post
  is_edited INTEGER DEFAULT 0, -- Boolean: indicates if post was edited
  edited_at TEXT,
  edited_by INTEGER REFERENCES users(id),
  edit_reason TEXT,
  is_deleted INTEGER DEFAULT 0, -- Boolean: soft delete
  deleted_at TEXT,
  deleted_by INTEGER REFERENCES users(id),
  deletion_reason TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Purpose**: Individual messages/posts within discussion topics

### 4. Board Permissions (Role-based Access Control)
```sql
CREATE TABLE board_permissions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  board_id INTEGER NOT NULL REFERENCES boards(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK(role IN ('admin', 'moderator', 'user')),
  can_view INTEGER DEFAULT 1, -- Boolean: can view board
  can_create_topics INTEGER DEFAULT 1, -- Boolean: can create new topics
  can_reply INTEGER DEFAULT 1, -- Boolean: can reply to topics
  can_edit_own INTEGER DEFAULT 1, -- Boolean: can edit own posts
  can_delete_own INTEGER DEFAULT 0, -- Boolean: can delete own posts
  can_moderate INTEGER DEFAULT 0, -- Boolean: can moderate content
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(board_id, role)
);
```

**Purpose**: Define what each user role can do in each board

### 5. Topic Subscriptions (Notification System)
```sql
CREATE TABLE topic_subscriptions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  topic_id INTEGER NOT NULL REFERENCES topics(id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  notification_type TEXT DEFAULT 'immediate' CHECK(notification_type IN ('immediate', 'daily', 'weekly', 'none')),
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(topic_id, user_id)
);
```

**Purpose**: Allow users to subscribe to topics for notifications

### 6. Post Reactions (Like/Dislike System)
```sql
CREATE TABLE post_reactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  post_id INTEGER NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  reaction_type TEXT NOT NULL CHECK(reaction_type IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry')),
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(post_id, user_id, reaction_type)
);
```

**Purpose**: Allow users to react to posts with emotions

### 7. Moderation Actions (Audit Trail for Moderation)
```sql
CREATE TABLE moderation_actions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  target_type TEXT NOT NULL CHECK(target_type IN ('topic', 'post', 'user')),
  target_id INTEGER NOT NULL,
  moderator_id INTEGER NOT NULL REFERENCES users(id),
  action_type TEXT NOT NULL CHECK(action_type IN ('pin', 'unpin', 'lock', 'unlock', 'archive', 'delete', 'edit', 'warn', 'suspend')),
  reason TEXT,
  details TEXT, -- JSON for additional data
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Purpose**: Track all moderation actions for transparency and audit

## Updates to Existing Tables

### Content Authorship Extension
```sql
-- Extend content_type enum to include message board content
-- This allows tracking admin-posted content in message boards
UPDATE content_authorship SET content_type = 'topic' WHERE content_type = 'topic';
UPDATE content_authorship SET content_type = 'post' WHERE content_type = 'post';
```

### Audit Logs Extension
```sql
-- Extend target_type enum to include message board entities
-- This integrates message board actions into existing audit system
UPDATE audit_logs SET target_type = 'board' WHERE target_type = 'board';
UPDATE audit_logs SET target_type = 'topic' WHERE target_type = 'topic';
UPDATE audit_logs SET target_type = 'post' WHERE target_type = 'post';
```

### Scheduled Content Extension
```sql
-- Extend content_type enum to include message board content
-- This allows scheduling message board posts
UPDATE scheduled_content SET content_type = 'topic' WHERE content_type = 'topic';
UPDATE scheduled_content SET content_type = 'post' WHERE content_type = 'post';
```

## Default Board Categories

### Suggested Initial Boards:
1. **General Discussion** (`general-discussion`)
   - Icon: `fa-comments`
   - Color: `#3B82F6`
   - Description: "General chat about Finn Wolfhard and community topics"

2. **Music** (`music`)
   - Icon: `fa-music`
   - Color: `#10B981`
   - Description: "Discuss Finn's music projects, Calpurnia, The Aubreys, and solo work"

3. **Acting Projects** (`acting-projects`)
   - Icon: `fa-film`
   - Color: `#F59E0B`
   - Description: "Talk about Stranger Things, IT, and other acting projects"

4. **Fan Art & Creativity** (`fan-art`)
   - Icon: `fa-palette`
   - Color: `#EF4444`
   - Description: "Share and discuss fan art, creative projects, and tributes"

5. **News & Updates** (`news-updates`)
   - Icon: `fa-newspaper`
   - Color: `#8B5CF6`
   - Description: "Latest news, interviews, and updates about Finn"

6. **Community Support** (`community-support`)
   - Icon: `fa-heart`
   - Color: `#EC4899`
   - Description: "Support, encouragement, and community assistance"

## Relationships and Constraints

### Foreign Key Relationships:
- `topics.board_id` → `boards.id`
- `topics.author_id` → `users.id`
- `topics.last_post_id` → `posts.id`
- `posts.topic_id` → `topics.id`
- `posts.parent_post_id` → `posts.id` (self-referencing for threading)
- `posts.author_id` → `users.id`
- `board_permissions.board_id` → `boards.id`
- `topic_subscriptions.topic_id` → `topics.id`
- `topic_subscriptions.user_id` → `users.id`
- `post_reactions.post_id` → `posts.id`
- `post_reactions.user_id` → `users.id`
- `moderation_actions.moderator_id` → `users.id`

### Indexes for Performance:
```sql
CREATE INDEX idx_topics_board_id ON topics(board_id);
CREATE INDEX idx_topics_author_id ON topics(author_id);
CREATE INDEX idx_topics_last_post_at ON topics(last_post_at DESC);
CREATE INDEX idx_posts_topic_id ON posts(topic_id);
CREATE INDEX idx_posts_author_id ON posts(author_id);
CREATE INDEX idx_posts_parent_post_id ON posts(parent_post_id);
CREATE INDEX idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX idx_board_permissions_board_role ON board_permissions(board_id, role);
CREATE INDEX idx_topic_subscriptions_user_id ON topic_subscriptions(user_id);
CREATE INDEX idx_post_reactions_post_id ON post_reactions(post_id);
CREATE INDEX idx_moderation_actions_target ON moderation_actions(target_type, target_id);
```

## Integration Points

### With Existing Systems:
1. **User Management**: Leverages existing user roles (admin, moderator, user)
2. **Audit Logging**: Extends existing audit system for message board actions
3. **Content Authorship**: Tracks admin-posted content in message boards
4. **Scheduled Content**: Allows scheduling of message board posts
5. **Theme System**: Message boards will inherit existing light/dark theme
6. **Accessibility**: Will maintain WCAG 2.1 AA compliance standards

### Security Considerations:
1. **Role-based Permissions**: Granular control over what each role can do
2. **Soft Deletes**: Content is marked as deleted rather than permanently removed
3. **Moderation Audit Trail**: All moderation actions are logged
4. **Content Validation**: Input sanitization and validation for all user content
5. **Rate Limiting**: Prevent spam and abuse through posting limits
