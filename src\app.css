@import 'tailwindcss';
@import '@fontsource/fira-mono';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

:root {
	--font-body:
		Arial, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
		'Open Sans', 'Helvetica Neue', sans-serif;
	--font-mono: 'Fira Mono', monospace;
	/* Legacy SvelteKit variables - keeping for compatibility but not using for theming */
	--color-bg-0: rgb(202, 216, 228);
	--color-bg-1: hsl(209, 36%, 86%);
	--color-bg-2: hsl(224, 44%, 95%);
	--color-theme-1: #ff3e00;
	--color-theme-2: #4075a6;
	--column-width: 42rem;
	--column-margin-top: 4rem;
	font-family: var(--font-body);
	/* Removed fixed color that conflicts with theme system */
}

body {
	min-height: 100vh;
	margin: 0;
	/* Theme-aware background - removed fixed background that conflicts with theme system */
}

h1,
h2,
p {
	font-weight: 400;
}

p {
	line-height: 1.5;
}

a {
	color: var(--color-interactive-primary, var(--color-theme-1));
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

h1 {
	font-size: 2rem;
	text-align: center;
}

h2 {
	font-size: 1rem;
}

pre {
	font-size: 16px;
	font-family: var(--font-mono);
	background-color: var(--color-surface-secondary, rgba(255, 255, 255, 0.45));
	border-radius: var(--border-radius-sm, 3px);
	box-shadow: var(--shadow-sm, 2px 2px 6px rgb(255 255 255 / 25%));
	padding: 0.5em;
	overflow-x: auto;
	color: var(--color-text-primary, var(--color-text));
}

.text-column {
	display: flex;
	max-width: 48rem;
	flex: 0.6;
	flex-direction: column;
	justify-content: center;
	margin: 0 auto;
}

input,
button {
	font-size: inherit;
	font-family: inherit;
}

button:focus:not(:focus-visible) {
	outline: none;
}

@media (min-width: 720px) {
	h1 {
		font-size: 2.4rem;
	}
}

.visually-hidden {
	border: 0;
	clip: rect(0 0 0 0);
	height: auto;
	margin: 0;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
	white-space: nowrap;
}
