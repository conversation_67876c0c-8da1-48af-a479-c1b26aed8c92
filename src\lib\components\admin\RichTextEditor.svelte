<script>
	import { onMount, createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	// Props
	export let value = '';
	export let placeholder = 'Enter content...';
	export let disabled = false;
	export let height = 400;

	// State
	/** @type {HTMLElement} */
	let editorContainer;
	/** @type {HTMLTextAreaElement} */
	let htmlTextarea;
	let isReady = false;
	let editorMode = 'visual'; // 'visual' or 'html'
	let htmlContent = '';
	let visualEditor = null;

	// Initialize HTML content from value
	$: if (value !== htmlContent && editorMode === 'visual') {
		htmlContent = value;
	}

	/**
	 * Toggle between visual and HTML editor modes
	 */
	function toggleEditorMode() {
		if (disabled) return;

		if (editorMode === 'visual') {
			// Switch to HTML mode - get content from visual editor
			if (visualEditor) {
				htmlContent = visualEditor.innerHTML;
				value = htmlContent;
			}
			editorMode = 'html';
			// Focus the HTML textarea after switching
			setTimeout(() => {
				if (htmlTextarea) {
					htmlTextarea.focus();
				}
			}, 100);
		} else {
			// Switch to visual mode - update visual editor with HTML content
			editorMode = 'visual';
			value = htmlContent;
			// Update visual editor content after mode switch
			setTimeout(() => {
				if (visualEditor && visualEditor.innerHTML !== htmlContent) {
					visualEditor.innerHTML = htmlContent;
				}
				// Focus the visual editor
				if (visualEditor) {
					visualEditor.focus();
				}
			}, 100);
		}

		dispatch('change', { content: value });
	}

	/**
	 * Handle keyboard shortcuts
	 */
	function handleKeydown(event) {
		// Ctrl/Cmd + Shift + H to toggle between modes
		if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'H') {
			event.preventDefault();
			toggleEditorMode();
		}

		// Ctrl/Cmd + Shift + F to format HTML (when in HTML mode)
		if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F' && editorMode === 'html') {
			event.preventDefault();
			formatHtmlContent();
		}
	}

	/**
	 * Handle HTML textarea input
	 */
	function handleHtmlInput(event) {
		const rawHtml = event.target.value;
		// Apply basic validation and sanitization
		const sanitizedHtml = validateHtml(rawHtml);
		htmlContent = sanitizedHtml;
		value = sanitizedHtml;
		dispatch('change', { content: value });
	}

	/**
	 * Basic HTML validation and sanitization
	 */
	function validateHtml(html) {
		// Basic validation - check for unclosed tags
		const openTags = html.match(/<[^\/][^>]*>/g) || [];
		const closeTags = html.match(/<\/[^>]*>/g) || [];

		// Simple validation - just warn about potential issues
		if (openTags.length !== closeTags.length) {
			console.warn('Potential unclosed HTML tags detected');
		}

		// Basic sanitization - remove script tags for security
		return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
	}

	/**
	 * Format HTML with basic indentation
	 */
	function formatHtml(html) {
		let formatted = html;
		let indent = 0;
		const tab = '  ';

		formatted = formatted.replace(/></g, '>\n<');

		const lines = formatted.split('\n');
		const formattedLines = lines.map(line => {
			const trimmed = line.trim();
			if (!trimmed) return '';

			if (trimmed.startsWith('</')) {
				indent = Math.max(0, indent - 1);
			}

			const result = tab.repeat(indent) + trimmed;

			if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.endsWith('/>')) {
				indent++;
			}

			return result;
		});

		return formattedLines.join('\n');
	}

	/**
	 * Format the HTML content in the textarea
	 */
	function formatHtmlContent() {
		if (editorMode === 'html' && htmlTextarea) {
			const formatted = formatHtml(htmlContent);
			htmlContent = formatted;
			value = formatted;
			dispatch('change', { content: value });
		}
	}

	// Create simple rich text editor with basic formatting
	function createSimpleEditor() {
		if (!editorContainer || isReady) {
			console.log('Cannot create simple editor: container missing or already ready');
			return;
		}

		console.log('Creating simple rich text editor...');
		editorContainer.innerHTML = '';

		// Create toolbar
		const toolbar = document.createElement('div');
		toolbar.className = 'simple-toolbar';
		toolbar.innerHTML = `
			<button type="button" data-command="bold" title="Bold"><strong>B</strong></button>
			<button type="button" data-command="italic" title="Italic"><em>I</em></button>
			<button type="button" data-command="underline" title="Underline"><u>U</u></button>
			<button type="button" data-command="insertUnorderedList" title="Bullet List">• List</button>
			<button type="button" data-command="insertOrderedList" title="Numbered List">1. List</button>
			<button type="button" data-command="createLink" title="Link">🔗</button>
		`;

		// Create contenteditable div
		const editor = document.createElement('div');
		editor.contentEditable = 'true';
		editor.innerHTML = value || '';
		editor.style.cssText = `
			min-height: ${height - 90}px;
			padding: 0.75rem;
			border: 1px solid var(--color-border-primary);
			border-top: none;
			border-radius: 0 0 4px 4px;
			font-family: inherit;
			font-size: 1rem;
			line-height: 1.5;
			outline: none;
			background-color: var(--color-surface-primary);
			color: var(--color-text-primary);
			transition: var(--transition-theme);
		`;

		// Store reference to visual editor
		visualEditor = editor;

		if (placeholder && !value) {
			editor.setAttribute('data-placeholder', placeholder);
		}

		// Handle toolbar clicks with better error handling
		toolbar.addEventListener('click', (e) => {
			e.preventDefault();
			const target = /** @type {HTMLElement} */ (e.target);
			const button = target && target.closest ? target.closest('button') : null;
			if (!button) return;

			const command = button.dataset.command;
			if (!command) return;

			try {
				if (command === 'createLink') {
					const url = prompt('Enter URL:');
					if (url) {
						document.execCommand(command, false, url);
					}
				} else {
					document.execCommand(command, false, '');
				}
				editor.focus();
			} catch (error) {
				console.warn('Command execution failed:', command, error);
			}
		});

		// Handle content changes
		editor.addEventListener('input', () => {
			value = editor.innerHTML;
			dispatch('change', { content: value });
		});

		// Handle paste to clean up formatting
		editor.addEventListener('paste', (e) => {
			e.preventDefault();
			const clipboardData = e.clipboardData;
			if (clipboardData) {
				const text = clipboardData.getData('text/plain');
				try {
					document.execCommand('insertText', false, text);
				} catch (error) {
					// Fallback for browsers that don't support execCommand
					const selection = window.getSelection();
					if (selection && selection.rangeCount > 0) {
						const range = selection.getRangeAt(0);
						range.deleteContents();
						range.insertNode(document.createTextNode(text));
						range.collapse(false);
						selection.removeAllRanges();
						selection.addRange(range);
					}
				}
			}
		});

		editorContainer.appendChild(toolbar);
		editorContainer.appendChild(editor);

		// Set ready state
		isReady = true;
		console.log('Simple rich text editor created successfully');

		// Focus the editor
		setTimeout(() => {
			editor.focus();
		}, 100);
	}

	// Update content when value prop changes
	$: if (isReady && editorContainer) {
		const editor = editorContainer.querySelector('[contenteditable]');
		if (editor && editor.innerHTML !== value) {
			editor.innerHTML = value || '';
		}
	}

	onMount(() => {
		// For now, skip TinyMCE and go directly to simple editor for reliability
		console.log('Initializing simple rich text editor...');
		setTimeout(() => {
			if (editorContainer) {
				createSimpleEditor();
			} else {
				console.error('Editor container not found, retrying...');
				setTimeout(() => {
					if (editorContainer) {
						createSimpleEditor();
					} else {
						console.error('Editor container still not found after retry');
					}
				}, 100);
			}
		}, 50);
	});
</script>

<div class="rich-text-editor">
	<!-- Mode Toggle Header -->
	<div class="editor-header">
		<div class="mode-tabs">
			<button
				type="button"
				class="mode-tab"
				class:active={editorMode === 'visual'}
				on:click={() => editorMode === 'html' && toggleEditorMode()}
				on:keydown={handleKeydown}
				{disabled}
				aria-label="Visual Editor Mode"
				title="Visual Editor (Ctrl+Shift+H to toggle)"
			>
				📝 Visual
			</button>
			<button
				type="button"
				class="mode-tab"
				class:active={editorMode === 'html'}
				on:click={() => editorMode === 'visual' && toggleEditorMode()}
				on:keydown={handleKeydown}
				{disabled}
				aria-label="HTML Source Mode"
				title="HTML Source Editor (Ctrl+Shift+H to toggle)"
			>
				&lt;/&gt; HTML Source
			</button>
		</div>
		{#if editorMode === 'html'}
			<button
				type="button"
				class="format-btn"
				on:click={formatHtmlContent}
				on:keydown={handleKeydown}
				{disabled}
				title="Format HTML (Ctrl+Shift+F)"
				aria-label="Format HTML code"
			>
				🎨 Format
			</button>
		{/if}
	</div>

	<!-- Visual Editor -->
	{#if editorMode === 'visual'}
		<div bind:this={editorContainer} class="editor-container visual-editor"></div>

		{#if !isReady}
			<div class="loading">
				<p>Loading editor...</p>
			</div>
		{/if}
	{/if}

	<!-- HTML Source Editor -->
	{#if editorMode === 'html'}
		<div class="html-editor-container">
			<textarea
				bind:this={htmlTextarea}
				bind:value={htmlContent}
				on:input={handleHtmlInput}
				on:keydown={handleKeydown}
				class="html-editor"
				{placeholder}
				{disabled}
				style="min-height: {height - 90}px;"
				aria-label="HTML Source Code Editor"
				spellcheck="false"
			></textarea>
			<div class="html-editor-info">
				<span class="editor-mode-indicator">HTML Source Mode</span>
				<span class="line-count">Lines: {htmlContent.split('\n').length}</span>
			</div>
		</div>
	{/if}
</div>

<style>
	.rich-text-editor {
		position: relative;
		width: 100%;
		border: 1px solid var(--color-border-primary);
		border-radius: 8px;
		overflow: hidden;
		background-color: var(--color-surface-primary);
	}

	/* Editor Header with Mode Tabs */
	.editor-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: var(--color-surface-secondary);
		border-bottom: 1px solid var(--color-border-primary);
		padding: 0.5rem;
	}

	.mode-tabs {
		display: flex;
		gap: 0.25rem;
	}

	.mode-tab {
		padding: 0.5rem 1rem;
		border: none;
		border-radius: 4px;
		background-color: transparent;
		color: var(--color-text-secondary);
		cursor: pointer;
		font-size: 0.9rem;
		font-weight: 500;
		transition: var(--transition-theme);
		display: flex;
		align-items: center;
		gap: 0.25rem;
	}

	.mode-tab:hover:not(:disabled) {
		background-color: var(--color-surface-tertiary);
		color: var(--color-text-primary);
	}

	.mode-tab.active {
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
	}

	.mode-tab:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.format-btn {
		padding: 0.5rem 1rem;
		border: 1px solid var(--color-border-primary);
		border-radius: 4px;
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		cursor: pointer;
		font-size: 0.9rem;
		transition: var(--transition-theme);
	}

	.format-btn:hover:not(:disabled) {
		background-color: var(--color-surface-tertiary);
	}

	.format-btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	/* Editor Containers */
	.editor-container {
		width: 100%;
	}

	.visual-editor {
		border: none;
	}

	/* HTML Editor Styles */
	.html-editor-container {
		position: relative;
		width: 100%;
	}

	.html-editor {
		width: 100%;
		padding: 0.75rem;
		border: none;
		border-radius: 0;
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
		font-size: 0.9rem;
		line-height: 1.5;
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		resize: vertical;
		outline: none;
		tab-size: 2;
		white-space: pre;
		overflow-wrap: normal;
		overflow-x: auto;
		transition: var(--transition-theme);
	}

	.html-editor:focus {
		background-color: var(--color-surface-primary);
		box-shadow: inset 0 0 0 2px var(--color-border-focus);
	}

	.html-editor:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		background-color: var(--color-surface-tertiary);
	}

	.html-editor-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.5rem 0.75rem;
		background-color: var(--color-surface-secondary);
		border-top: 1px solid var(--color-border-primary);
		font-size: 0.8rem;
		color: var(--color-text-secondary);
	}

	.editor-mode-indicator {
		font-weight: 500;
		color: var(--color-interactive-primary);
	}

	.line-count {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
	}

	.loading {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: var(--color-bg-overlay);
		border: 1px solid var(--color-border-primary);
		border-radius: 4px;
		min-height: 200px;
	}

	.loading p {
		margin: 0;
		color: var(--color-text-secondary);
	}

	/* Styles for simple editor toolbar */
	:global(.simple-toolbar) {
		display: flex;
		gap: 0.5rem;
		padding: 0.5rem;
		background-color: var(--color-surface-secondary);
		border-bottom: 1px solid var(--color-border-primary);
		margin: 0;
	}

	:global(.simple-toolbar button) {
		padding: 0.25rem 0.5rem;
		border: 1px solid var(--color-border-secondary);
		border-radius: 3px;
		background-color: var(--color-surface-primary);
		color: var(--color-text-primary);
		cursor: pointer;
		font-size: 0.9rem;
		transition: var(--transition-theme);
	}

	:global(.simple-toolbar button:hover) {
		background-color: var(--color-surface-tertiary);
	}

	:global(.simple-toolbar button:active) {
		background-color: var(--color-surface-quaternary);
	}

	/* Contenteditable styles */
	:global([contenteditable="true"]) {
		border: none !important;
		border-radius: 0 !important;
	}

	:global([contenteditable="true"]:empty:before) {
		content: attr(data-placeholder);
		color: var(--color-text-muted);
		font-style: italic;
	}

	:global([contenteditable="true"]:focus) {
		outline: none;
		box-shadow: inset 0 0 0 2px var(--color-border-focus);
	}

	/* Syntax highlighting for HTML in textarea (basic) */
	.html-editor {
		/* Basic syntax highlighting through CSS is limited, but we can style the editor */
		font-variant-ligatures: none;
		font-feature-settings: normal;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.editor-header {
			flex-direction: column;
			gap: 0.5rem;
			align-items: stretch;
		}

		.mode-tabs {
			justify-content: center;
		}

		.format-btn {
			align-self: center;
		}

		.html-editor-info {
			flex-direction: column;
			gap: 0.25rem;
			text-align: center;
		}
	}

	/* Global styles for TinyMCE (if used) */
	:global(.tox-tinymce) {
		border: none !important;
		border-radius: 0 !important;
	}

	:global(.tox-toolbar) {
		border-bottom: 1px solid var(--color-border-primary) !important;
		background-color: var(--color-surface-secondary) !important;
	}

	:global(.tox-edit-area) {
		border: none !important;
		background-color: var(--color-surface-primary) !important;
	}

	:global(.tox-edit-area iframe) {
		background-color: var(--color-surface-primary) !important;
	}
</style>
