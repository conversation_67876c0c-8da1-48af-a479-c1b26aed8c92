import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import { boards, topics, posts, auditLogs } from '$lib/server/db/schema';
import { eq, sql } from 'drizzle-orm';

// GET - List all boards with statistics
export const GET: RequestHandler = async ({ locals }) => {
	try {
		// Check authentication
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ success: false, error: 'Unauthorized' }, { status: 401 });
		}

		// Get boards with topic and post counts
		const boardsWithStats = await db
			.select({
				id: boards.id,
				name: boards.name,
				description: boards.description,
				slug: boards.slug,
				icon: boards.icon,
				color: boards.color,
				position: boards.position,
				isActive: boards.isActive,
				createdAt: boards.createdAt,
				updatedAt: boards.updatedAt,
				topicCount: sql<number>`(
					SELECT COUNT(*) FROM ${topics} 
					WHERE ${topics.boardId} = ${boards.id}
				)`,
				postCount: sql<number>`(
					SELECT COUNT(*) FROM ${posts} 
					WHERE ${posts.topicId} IN (
						SELECT ${topics.id} FROM ${topics} 
						WHERE ${topics.boardId} = ${boards.id}
					)
				)`
			})
			.from(boards)
			.orderBy(boards.position, boards.createdAt);

		return json({
			success: true,
			data: boardsWithStats
		});

	} catch (error) {
		console.error('Error fetching boards:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};

// POST - Create new board
export const POST: RequestHandler = async ({ request, locals, getClientAddress }) => {
	try {
		// Check authentication
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ success: false, error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { name, description, slug, icon, color, position, isActive } = body;

		// Validate required fields
		if (!name || !slug) {
			return json({ success: false, error: 'Name and slug are required' }, { status: 400 });
		}

		// Validate slug format
		const slugRegex = /^[a-z0-9-]+$/;
		if (!slugRegex.test(slug)) {
			return json({ 
				success: false, 
				error: 'Slug must contain only lowercase letters, numbers, and hyphens' 
			}, { status: 400 });
		}

		// Check if slug already exists
		const existingBoard = await db
			.select({ id: boards.id })
			.from(boards)
			.where(eq(boards.slug, slug))
			.limit(1);

		if (existingBoard.length > 0) {
			return json({ success: false, error: 'A board with this slug already exists' }, { status: 400 });
		}

		// Create the board
		const currentTime = new Date().toISOString();
		const [newBoard] = await db
			.insert(boards)
			.values({
				name: name.trim(),
				description: description?.trim() || null,
				slug: slug.trim(),
				icon: icon?.trim() || null,
				color: color?.trim() || '#3B82F6',
				position: position || 0,
				isActive: isActive !== false, // Default to true
				createdAt: currentTime,
				updatedAt: currentTime
			})
			.returning();

		// Log the action
		await db
			.insert(auditLogs)
			.values({
				adminId: locals.user.id,
				action: 'create_board',
				targetType: 'board',
				targetId: newBoard.id,
				details: JSON.stringify({
					boardName: name,
					boardSlug: slug
				}),
				ipAddress: getClientAddress(),
				userAgent: request.headers.get('user-agent') || '',
				riskLevel: 'low',
				createdAt: currentTime
			});

		return json({
			success: true,
			message: 'Board created successfully',
			data: newBoard
		});

	} catch (error) {
		console.error('Error creating board:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};
