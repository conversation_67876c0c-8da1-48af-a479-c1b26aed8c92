const Database = require('better-sqlite3');
const path = require('path');

// Database connection
const db = new Database('local.db');

console.log('🚀 Starting FWFC Message Board System Migration...');

try {
  // Enable foreign keys
  db.pragma('foreign_keys = ON');

  // Create boards table
  console.log('Creating boards table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS boards (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      slug TEXT UNIQUE NOT NULL,
      icon TEXT,
      color TEXT,
      position INTEGER DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create topics table
  console.log('Creating topics table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS topics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      board_id INTEGER NOT NULL REFERENCES boards(id) ON DELETE CASCADE,
      title TEXT NOT NULL,
      slug TEXT NOT NULL,
      description TEXT,
      author_id INTEGER NOT NULL REFERENCES users(id),
      is_pinned INTEGER DEFAULT 0,
      is_locked INTEGER DEFAULT 0,
      is_archived INTEGER DEFAULT 0,
      view_count INTEGER DEFAULT 0,
      post_count INTEGER DEFAULT 0,
      last_post_id INTEGER REFERENCES posts(id),
      last_post_at TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(board_id, slug)
    )
  `);

  // Create posts table
  console.log('Creating posts table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS posts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      topic_id INTEGER NOT NULL REFERENCES topics(id) ON DELETE CASCADE,
      parent_post_id INTEGER REFERENCES posts(id),
      author_id INTEGER NOT NULL REFERENCES users(id),
      content TEXT NOT NULL,
      is_first_post INTEGER DEFAULT 0,
      is_edited INTEGER DEFAULT 0,
      edited_at TEXT,
      edited_by INTEGER REFERENCES users(id),
      edit_reason TEXT,
      is_deleted INTEGER DEFAULT 0,
      deleted_at TEXT,
      deleted_by INTEGER REFERENCES users(id),
      deletion_reason TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create board_permissions table
  console.log('Creating board_permissions table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS board_permissions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      board_id INTEGER NOT NULL REFERENCES boards(id) ON DELETE CASCADE,
      role TEXT NOT NULL CHECK(role IN ('admin', 'moderator', 'user')),
      can_view INTEGER DEFAULT 1,
      can_create_topics INTEGER DEFAULT 1,
      can_reply INTEGER DEFAULT 1,
      can_edit_own INTEGER DEFAULT 1,
      can_delete_own INTEGER DEFAULT 0,
      can_moderate INTEGER DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(board_id, role)
    )
  `);

  // Create topic_subscriptions table
  console.log('Creating topic_subscriptions table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS topic_subscriptions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      topic_id INTEGER NOT NULL REFERENCES topics(id) ON DELETE CASCADE,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      notification_type TEXT DEFAULT 'immediate' CHECK(notification_type IN ('immediate', 'daily', 'weekly', 'none')),
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(topic_id, user_id)
    )
  `);

  // Create post_reactions table
  console.log('Creating post_reactions table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS post_reactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      post_id INTEGER NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      reaction_type TEXT NOT NULL CHECK(reaction_type IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry')),
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(post_id, user_id, reaction_type)
    )
  `);

  // Create moderation_actions table
  console.log('Creating moderation_actions table...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS moderation_actions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      target_type TEXT NOT NULL CHECK(target_type IN ('topic', 'post', 'user')),
      target_id INTEGER NOT NULL,
      moderator_id INTEGER NOT NULL REFERENCES users(id),
      action_type TEXT NOT NULL CHECK(action_type IN ('pin', 'unpin', 'lock', 'unlock', 'archive', 'delete', 'edit', 'warn', 'suspend')),
      reason TEXT,
      details TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create indexes for performance
  console.log('Creating indexes...');
  db.exec(`CREATE INDEX IF NOT EXISTS idx_topics_board_id ON topics(board_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_topics_author_id ON topics(author_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_topics_last_post_at ON topics(last_post_at DESC)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_posts_topic_id ON posts(topic_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_posts_author_id ON posts(author_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_posts_parent_post_id ON posts(parent_post_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_board_permissions_board_role ON board_permissions(board_id, role)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_topic_subscriptions_user_id ON topic_subscriptions(user_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_post_reactions_post_id ON post_reactions(post_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_moderation_actions_target ON moderation_actions(target_type, target_id)`);

  // Insert default boards
  console.log('Creating default boards...');
  const insertBoard = db.prepare(`
    INSERT OR IGNORE INTO boards (name, description, slug, icon, color, position)
    VALUES (?, ?, ?, ?, ?, ?)
  `);

  const defaultBoards = [
    ['General Discussion', 'General chat about Finn Wolfhard and community topics', 'general-discussion', 'fa-comments', '#3B82F6', 1],
    ['Music', 'Discuss Finn\'s music projects, Calpurnia, The Aubreys, and solo work', 'music', 'fa-music', '#10B981', 2],
    ['Acting Projects', 'Talk about Stranger Things, IT, and other acting projects', 'acting-projects', 'fa-film', '#F59E0B', 3],
    ['Fan Art & Creativity', 'Share and discuss fan art, creative projects, and tributes', 'fan-art', 'fa-palette', '#EF4444', 4],
    ['News & Updates', 'Latest news, interviews, and updates about Finn', 'news-updates', 'fa-newspaper', '#8B5CF6', 5],
    ['Community Support', 'Support, encouragement, and community assistance', 'community-support', 'fa-heart', '#EC4899', 6]
  ];

  for (const board of defaultBoards) {
    insertBoard.run(...board);
  }

  // Insert default board permissions for each role
  console.log('Setting up default board permissions...');
  const boardIds = db.prepare('SELECT id FROM boards').all();
  const insertPermission = db.prepare(`
    INSERT OR IGNORE INTO board_permissions (board_id, role, can_view, can_create_topics, can_reply, can_edit_own, can_delete_own, can_moderate)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  for (const board of boardIds) {
    // Admin permissions (full access)
    insertPermission.run(board.id, 'admin', 1, 1, 1, 1, 1, 1);
    
    // Moderator permissions (moderate but not full admin)
    insertPermission.run(board.id, 'moderator', 1, 1, 1, 1, 1, 1);
    
    // User permissions (standard user access)
    insertPermission.run(board.id, 'user', 1, 1, 1, 1, 0, 0);
  }

  console.log('✅ Message board system migration completed successfully!');
  console.log(`📊 Created ${defaultBoards.length} default boards with permissions for all user roles.`);

} catch (error) {
  console.error('❌ Error during message board migration:', error);
  process.exit(1);
} finally {
  db.close();
}
